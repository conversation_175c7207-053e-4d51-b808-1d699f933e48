"""Configuration for the equipment inventory management application."""
import os
import logging
from datetime import datetime
from pathlib import Path
import sys

# Application information
APP_NAME = "InventoryTracker"
APP_VERSION = "1.0.0"
def get_app_icon():
    """Get application icon, with fallback options."""
    # Check for optimized PNG first
    png_icon = Path("resources/app_icon.png")
    if png_icon.exists() and png_icon.stat().st_size < 100*1024:  # Less than 100KB
        return str(png_icon)
    
    # Fallback to SVG if PNG doesn't exist or is too large
    svg_icon = Path("resources/app_icon.svg")
    if svg_icon.exists():
        return str(svg_icon)
    
    # No icon found
    return None

APP_ICON = get_app_icon()

# Robust database and logging configuration: always use user-writable directory and fallback if needed
import getpass

def get_user_writable_dir():
    """Get user writable directory using secure Path objects."""
    # Handle PyInstaller executable environment
    if getattr(sys, 'frozen', False):
        # Running as compiled executable
        # Use a more reliable path for executables
        local_app_data = os.getenv("LOCALAPPDATA")
        if local_app_data:
            base_dir = Path(local_app_data)
        else:
            # Fallback to user home directory
            base_dir = Path.home() / "AppData" / "Local"
    else:
        # Running in development
        local_app_data = os.getenv("LOCALAPPDATA")
        if local_app_data:
            base_dir = Path(local_app_data)
        else:
            base_dir = Path.home()
    
    user_dir = base_dir / APP_NAME
    
    # Ensure directory exists with proper error handling
    try:
        user_dir.mkdir(parents=True, exist_ok=True)
    except OSError as e:
        # If we can't create in LocalAppData, try temp directory
        import tempfile
        temp_base = Path(tempfile.gettempdir())
        user_dir = temp_base / APP_NAME
        user_dir.mkdir(parents=True, exist_ok=True)
        print(f"Warning: Using temp directory for app data: {user_dir}")
    
    return str(user_dir)

def get_log_path():
    """Get log file path with validation."""
    # Allow override via environment variable
    env_log_path = os.getenv("INVENTORY_LOG_PATH")
    if env_log_path:
        log_path = Path(env_log_path)
        # Validate the path is safe
        if validate_file_path(log_path, allow_create=True):
            return str(log_path)
        else:
            print(f"Warning: Invalid log path in environment variable: {env_log_path}")
    
    try:
        user_dir = Path(get_user_writable_dir())
        return str(user_dir / "inventory_app.log")
    except Exception as e:
        # Fallback to current directory if all else fails
        print(f"Warning: Could not determine user directory for logs: {e}")
        return "inventory_app.log"

def get_db_path():
    """Get database file path with validation."""
    # Allow override via environment variable
    env_db_path = os.getenv("INVENTORY_DB_PATH")
    if env_db_path:
        db_path = Path(env_db_path)
        # Validate the path is safe
        if validate_file_path(db_path, allow_create=True):
            return str(db_path)
        else:
            print(f"Warning: Invalid database path in environment variable: {env_db_path}")
    
    try:
        user_dir = Path(get_user_writable_dir())
        return str(user_dir / "inventory.db")
    except Exception as e:
        # Fallback to current directory if all else fails
        print(f"Warning: Could not determine user directory for database: {e}")
        # For executable, try to use the directory where executable is located
        if getattr(sys, 'frozen', False):
            exe_dir = Path(sys.executable).parent
            return str(exe_dir / "inventory.db")
        else:
            return "inventory.db"

def validate_file_path(file_path, allow_create=False):
    """
    Validate file path for security and accessibility.
    
    Args:
        file_path (Path): Path object to validate
        allow_create (bool): Whether to allow non-existent files
        
    Returns:
        bool: True if path is valid and safe
    """
    try:
        # Convert to absolute path and resolve
        abs_path = file_path.resolve()
        
        # Check if path tries to escape user directory
        user_base = Path(get_user_writable_dir()).resolve()
        temp_base = Path.home() / "AppData" / "Local" / "Temp"
        
        # Allow paths within user directory or temp
        if not (str(abs_path).startswith(str(user_base)) or 
                str(abs_path).startswith(str(temp_base))):
            return False
        
        # Check parent directory exists or can be created
        parent = abs_path.parent
        if not parent.exists():
            if allow_create:
                try:
                    parent.mkdir(parents=True, exist_ok=True)
                except OSError:
                    return False
            else:
                return False
        
        # Check if file exists (if it should)
        if not allow_create and not abs_path.exists():
            return False
            
        return True
        
    except (OSError, ValueError):
        return False

# Environment-configurable paths
LOG_PATH = get_log_path()
DB_PATH = get_db_path()

# Environment-configurable settings
DEBUG_MODE = os.getenv("INVENTORY_DEBUG", "false").lower() == "true"
MAX_LOG_SIZE_MB = int(os.getenv("INVENTORY_MAX_LOG_SIZE_MB", "5"))
LOG_BACKUP_COUNT = int(os.getenv("INVENTORY_LOG_BACKUP_COUNT", "3"))

LOG_LEVEL = logging.INFO  # Changed from DEBUG to reduce log file size
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# Logging initialization moved to bottom of file to avoid duplication

# Date format for display
DATE_FORMAT = '%Y-%m-%d'

# Window Configuration - Responsive Design
# Base dimensions that scale with screen size
def get_screen_dimensions():
    """Get screen dimensions for responsive window sizing."""
    try:
        # Try to get screen dimensions using Windows API
        import ctypes
        user32 = ctypes.windll.user32
        screen_width = user32.GetSystemMetrics(0)  # SM_CXSCREEN
        screen_height = user32.GetSystemMetrics(1)  # SM_CYSCREEN
        return screen_width, screen_height
    except:
        # Fallback dimensions if Windows API fails
        return 1920, 1080

SCREEN_WIDTH, SCREEN_HEIGHT = get_screen_dimensions()

# Window dimensions as percentages of screen size for better scaling
MAIN_WINDOW_WIDTH_PERCENT = 0.75  # 75% of screen width
MAIN_WINDOW_HEIGHT_PERCENT = 0.8  # 80% of screen height
MAIN_WINDOW_MIN_WIDTH_PERCENT = 0.6  # 60% of screen width minimum
MAIN_WINDOW_MIN_HEIGHT_PERCENT = 0.6  # 60% of screen height minimum

# Calculate actual dimensions
MAIN_WINDOW_WIDTH = int(SCREEN_WIDTH * MAIN_WINDOW_WIDTH_PERCENT)
MAIN_WINDOW_HEIGHT = int(SCREEN_HEIGHT * MAIN_WINDOW_HEIGHT_PERCENT)
MAIN_WINDOW_MIN_WIDTH = int(SCREEN_WIDTH * MAIN_WINDOW_MIN_WIDTH_PERCENT)
MAIN_WINDOW_MIN_HEIGHT = int(SCREEN_HEIGHT * MAIN_WINDOW_MIN_HEIGHT_PERCENT)

# Ensure minimum reasonable sizes
MAIN_WINDOW_WIDTH = max(MAIN_WINDOW_WIDTH, 1000)
MAIN_WINDOW_HEIGHT = max(MAIN_WINDOW_HEIGHT, 700)
MAIN_WINDOW_MIN_WIDTH = max(MAIN_WINDOW_MIN_WIDTH, 800)
MAIN_WINDOW_MIN_HEIGHT = max(MAIN_WINDOW_MIN_HEIGHT, 600)

# Dialog and popup window sizing
DIALOG_WIDTH_PERCENT = 0.5  # 50% of screen width
DIALOG_HEIGHT_PERCENT = 0.6  # 60% of screen height
DIALOG_MIN_WIDTH = 400
DIALOG_MIN_HEIGHT = 300

# Tab widget sizing preferences
TAB_CONTENT_MARGINS = (10, 10, 10, 10)  # left, top, right, bottom
TAB_SPACING = 5

# Default top-up percent for fluids (can be overridden per-fluid)
DEFAULT_TOP_UP_PERCENT = 10

# Current fiscal year (format: YYYY-YY)
current_year = datetime.now().year
next_year = current_year + 1
CURRENT_FISCAL_YEAR = f"{current_year}-{str(next_year)[-2:]}"

# UI style
UI_STYLE = """
    QMainWindow {
        background-color: #f5f5f5;
    }
    QTabWidget::pane {
        border: 1px solid #cccccc;
        background-color: white;
        border-radius: 5px;
    }
    QTabBar::tab {
        background-color: #e0e0e0;
        color: #333333;
        padding: 8px 16px;
        border: 1px solid #cccccc;
        border-bottom: none;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        margin-right: 2px;
    }
    QTabBar::tab:selected {
        background-color: white;
        border-bottom-color: white;
    }
    QTabBar::tab:hover:!selected {
        background-color: #eeeeee;
    }
    QGroupBox {
        font-weight: bold;
        border: 1px solid #cccccc;
        border-radius: 5px;
        margin-top: 1.5ex;
        padding-top: 1.5ex;
    }
    QGroupBox::title {
        subcontrol-origin: margin;
        subcontrol-position: top center;
        padding: 0 5px;
    }
    QPushButton {
        background-color: #0078d7;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 6px 12px;
        font-weight: bold;
    }
    QPushButton:hover {
        background-color: #005a9e;
    }
    QPushButton:disabled {
        background-color: #cccccc;
        color: #888888;
    }
    QLineEdit, QSpinBox, QDoubleSpinBox, QDateEdit, QComboBox, QTextEdit {
        border: 1px solid #cccccc;
        border-radius: 4px;
        padding: 4px;
        background-color: white;
    }
    QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus, QComboBox:focus, QTextEdit:focus {
        border: 1px solid #0078d7;
    }
    QLineEdit:read-only {
        background-color: #f0f0f0;
    }
    QScrollArea {
        border: none;
    }
"""

# Initialize logging
def init_logging():
    """Initialize application logging with rotation - simplified for Python 3.13 compatibility."""
    # Skip if already configured to avoid conflicts with main.py
    if logging.getLogger().hasHandlers():
        return
        
    # Simple console-only logging to avoid recursion issues
    logging.basicConfig(
        level=LOG_LEVEL,
        format='%(name)s - %(levelname)s - %(message)s'
    )
    
    # Set more restrictive log levels for some loggers
    logging.getLogger('matplotlib').setLevel(logging.WARNING)
    logging.getLogger('PIL').setLevel(logging.WARNING)

# Call init_logging when importing this module
init_logging()

# BA Number Configuration
import re

BA_NUMBER_VALIDATION_REGEX = r'^[0-9]{2}[A-Z]{1}[0-9A-Z]+$'  # Matches patterns like 86R3124A, 95R5843H, 09E020651H
REQUIRE_BA_NUMBER = False  # Make BA number optional during transition
BA_NUMBER_DISPLAY_FORMAT = "BA: {ba_number}"

def validate_ba_number(ba_number):
    """
    Validate BA number format and content.
    
    Args:
        ba_number (str): The BA number to validate
        
    Returns:
        tuple: (is_valid, error_message)
    """
    if not ba_number and not REQUIRE_BA_NUMBER:
        return True, None
    
    if not ba_number and REQUIRE_BA_NUMBER:
        return False, "BA number is required"
    
    ba_number = ba_number.strip().upper()
    
    # Check length (minimum 4 characters)
    if len(ba_number) < 4:
        return False, "BA number must be at least 4 characters long"
    
    # Check maximum length (reasonable limit)
    if len(ba_number) > 15:
        return False, "BA number cannot exceed 15 characters"
    
    # Check format using regex
    if not re.match(BA_NUMBER_VALIDATION_REGEX, ba_number):
        return False, "BA number format is invalid. Expected format: ##X#####... (e.g., 86R3124A)"
    
    return True, None

def sanitize_ba_number(ba_number):
    """
    Sanitize BA number input by removing invalid characters and normalizing format.
    
    Args:
        ba_number (str): Raw BA number input
        
    Returns:
        str: Sanitized BA number
    """
    if not ba_number:
        return ""
    
    # Remove whitespace and convert to uppercase
    ba_number = ba_number.strip().upper()
    
    # Remove non-alphanumeric characters
    ba_number = re.sub(r'[^0-9A-Z]', '', ba_number)
    
    return ba_number

# Maintenance configuration
MAINTENANCE_CRITICAL_DAYS = 7     # Under 7 days = Critical
MAINTENANCE_WARNING_DAYS = 30     # Under 30 days = Warning
MAINTENANCE_UPCOMING_DAYS = 90    # Under 3 months (90 days) = Upcoming

# Maintenance categories and their periods
MAINTENANCE_CATEGORIES = {
    'TM-1': {'months': 6, 'display': 'TM-1 (Half Yearly)'},
    'TM-2': {'months': 12, 'display': 'TM-2 (Yearly)'},
    'Yearly': {'months': 12, 'display': 'Yearly'},
    'Monthly': {'months': 1, 'display': 'Monthly'}
}

# Maintenance types for each category
MAINTENANCE_TYPES = {
    'TM-1': ['TM-I', 'Half Yearly Service', 'Semi-Annual Check'],
    'TM-2': ['TM-II', 'Annual Service', 'Yearly Overhaul'],
    'Yearly': ['Annual Check', 'Yearly Inspection', 'Annual Maintenance'],
    'Monthly': ['Monthly Service', 'Monthly Check', 'Monthly Inspection']
}

# Discard criteria configuration - Updated with official military rules
DISCARD_CRITERIA_RULES = {
    # Military Equipment - First 11 entries use "All (Later)" logic - Exact names as provided
    'MOTOR CYCLE (HH/RE)': {'years': 15, 'kms': 75000, 'hours': 0, 'logic': 'LATER'},
    'TATA SAFARI': {'years': 9, 'kms': 100000, 'hours': 0, 'logic': 'LATER'},
    'MAHINDRA SCORPIO': {'years': 15, 'kms': 100000, 'hours': 0, 'logic': 'LATER'},
    'MARUTI GYPSY': {'years': 9, 'kms': 100000, 'hours': 0, 'logic': 'LATER'},
    'ALS (AMB)': {'years': 9, 'kms': 60000, 'hours': 0, 'logic': 'LATER'},
    'ALS GS VEH': {'years': 9, 'kms': 100000, 'hours': 0, 'logic': 'LATER'},
    'TATA 2.5 TON': {'years': 9, 'kms': 100000, 'hours': 0, 'logic': 'LATER'},
    'TATA 2.5 TON 2KL WB': {'years': 9, 'kms': 100000, 'hours': 0, 'logic': 'LATER'},
    'ARMY BUS': {'years': 15, 'kms': 110000, 'hours': 0, 'logic': 'LATER'},
    'ALS LRV': {'years': 9, 'kms': 60000, 'hours': 0, 'logic': 'LATER'},
    'ALS 5 KL WB': {'years': 9, 'kms': 60000, 'hours': 0, 'logic': 'LATER'},  # Special: "and" logic
    'DOZER': {'years': 18, 'kms': 0, 'hours': 2000},
    'DOZER D-80 A-12': {'years': 18, 'kms': 0, 'hours': 2000},
    'JCB ALL TYPES': {'years': 18, 'kms': 0, 'hours': 2000},
    'SSL': {'years': 18, 'kms': 0, 'hours': 2000},
    'GENERATOR SET UPTO 5KVA': {'years': 9, 'kms': 0, 'hours': 7000},
    'GENERATOR 5KVA TO 30 KVA': {'years': 12, 'kms': 0, 'hours': 7000},
    'GENERATOR 30 KVA ABOVE': {'years': 15, 'kms': 0, 'hours': 7000},  # Fixed: was 7000 kms, now 7000 hrs
    'BMP-I, BMP-II AND AERV': {'years': 35, 'kms': 7900, 'hours': 0},
}

DISCARD_CRITERIA_PRIORITY = [
    # Most specific patterns first (longest names)
    'TATA 2.5 TON 2KL WB',
    'GENERATOR 30 KVA ABOVE',
    'GENERATOR 5KVA TO 30 KVA', 
    'GENERATOR SET UPTO 5KVA',
    'BMP-I, BMP-II AND AERV',
    'MOTOR CYCLE (HH/RE)',
    'DOZER D-80 A-12',
    'MAHINDRA SCORPIO',
    'JCB ALL TYPES',
    'TATA 2.5 TON',
    'TATA SAFARI',
    'MARUTI GYPSY',
    'ALS 5 KL WB',
    'ALS GS VEH',
    'ALS (AMB)',
    'ALS LRV',
    'ARMY BUS',
    'DOZER',
    'SSL',
]

# Discard criteria evaluation settings
DISCARD_WARNING_THRESHOLD = 0.9  # Warn when 90% of criteria reached
DISCARD_CRITICAL_THRESHOLD = 1.0  # Critical when criteria exceeded
DISCARD_NOTIFICATION_ENABLED = True
DISCARD_NOTIFICATION_DAYS = [30, 7, 1]  # Days before discard to send notifications