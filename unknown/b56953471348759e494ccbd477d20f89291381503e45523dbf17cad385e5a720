/* Main application styles */
QMainWindow {
    background-color: #f5f5f5;
}

/* Tab styles */
QTabWidget::pane {
    border: 1px solid #cccccc;
    background-color: white;
    border-radius: 5px;
}

QTabBar::tab {
    background-color: #e0e0e0;
    color: #333333;
    padding: 8px 16px;
    border: 1px solid #cccccc;
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    margin-right: 2px;
}

QTabBar::tab:selected {
    background-color: white;
    border-bottom-color: white;
}

QTabBar::tab:hover:!selected {
    background-color: #eeeeee;
}

/* Group box styles */
QGroupBox {
    font-weight: bold;
    border: 1px solid #cccccc;
    border-radius: 5px;
    margin-top: 1.5ex;
    padding-top: 1.5ex;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 5px;
}

/* Button styles */
QPushButton {
    background-color: #0078d7;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #005a9e;
}

QPushButton:disabled {
    background-color: #cccccc;
    color: #888888;
}

/* Input field styles */
QLineEdit, QSpinBox, QDoubleSpinBox, QDateEdit, QComboBox, QTextEdit {
    border: 1px solid #cccccc;
    border-radius: 4px;
    padding: 4px;
    background-color: white;
}

QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus, QComboBox:focus, QTextEdit:focus {
    border: 1px solid #0078d7;
}

QLineEdit:read-only {
    background-color: #f0f0f0;
}

/* Scroll area */
QScrollArea {
    border: none;
}

/* Dashboard tile styles */
QFrame#dashboardTile {
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: white;
    padding: 10px;
}

/* Table styles */
QTableWidget {
    border: 1px solid #d9d9d9;
    border-radius: 2px;
    selection-background-color: #e6f2ff;
    selection-color: #000;
}

QHeaderView::section {
    background-color: #f0f0f0;
    border: 1px solid #d9d9d9;
    padding: 4px;
    font-weight: bold;
}

/* Status colors */
.status-normal {
    color: #4CAF50;
}

.status-warning {
    color: #FFC107;
}

.status-critical {
    color: #F44336;
}

.status-inactive {
    color: #9E9E9E;
}

.status-unknown {
    color: #2196F3;
}

/* Alert styles */
QFrame#alertTile {
    border: 1px solid #FFC107;
    border-radius: 5px;
    background-color: #FFFDE7;
    margin-bottom: 5px;
    padding: 10px;
}

QFrame#criticalAlertTile {
    border: 1px solid #F44336;
    border-radius: 5px;
    background-color: #FFEBEE;
    margin-bottom: 5px;
    padding: 10px;
}

/* Chart section */
QFrame#chartFrame {
    border: 1px solid #d9d9d9;
    border-radius: 5px;
    background-color: white;
    padding: 5px;
}

/* Detail view */
QFrame#detailFrame {
    border: 1px solid #d9d9d9;
    border-radius: 5px;
    background-color: white;
    padding: 10px;
}

/* Splitter */
QSplitter::handle {
    background-color: #cccccc;
}

QSplitter::handle:horizontal {
    width: 1px;
}

QSplitter::handle:vertical {
    height: 1px;
}

/* Labels */
QLabel#title {
    font-size: 14px;
    font-weight: bold;
    color: #333333;
}

QLabel#count {
    font-size: 24px;
    font-weight: bold;
    color: #0078d7;
}

QLabel#statusLabel {
    padding: 3px 8px;
    border-radius: 4px;
    font-weight: bold;
    color: white;
}

/* Tooltips */
QToolTip {
    border: 1px solid #0078d7;
    background-color: #f0f5ff;
    color: #333333;
    padding: 4px;
    border-radius: 2px;
}