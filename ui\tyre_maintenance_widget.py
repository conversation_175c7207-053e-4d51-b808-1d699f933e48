"""Conditioning management widget for the equipment inventory application."""
import logging
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                           QPushButton, QTableWidget, QTableWidgetItem,
                           QHeaderView, QAbstractItemView, QMessageBox,
                           QComboBox, QLineEdit, QFormLayout, QGroupBox,
                           QSplitter, QFrame, QDateEdit, QSpinBox, QTabWidget,
                           QDialog)
from PyQt5.QtCore import Qt, QSize, QDate, pyqtSignal, QTimer
from PyQt5.QtGui import QIcon, QColor

import models
from ui.custom_widgets import ReadOnlyTableWidget, StatusLabel
from ui.dialogs import ConditioningDialog, MaintenanceDialog, BatteryDialog
import utils
from datetime import date, timedelta
import database
from ui.common_styles import *

logger = logging.getLogger(__name__)

class ConditioningWidget(QWidget):
    """Widget for managing conditioning."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """Set up the conditioning widget UI."""
        # Main layout
        main_layout = QVBoxLayout(self)
        apply_standard_layout(main_layout)
        
        # Title
        title_label = QLabel("Conditioning Management")
        title_label.setObjectName("titleLabel")
        main_layout.addWidget(title_label)
        
        # Create tab widget for sub-tabs
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # Create sub-tabs
        self.setup_tyre_maintenance_tab()
        self.setup_battery_tab()
        
        # Add tabs to tab widget
        self.tab_widget.addTab(self.tyre_maintenance_widget, "Tyre Maintenance")
        self.tab_widget.addTab(self.battery_widget, "Battery")

        # Apply standardized stylesheet
        self.setStyleSheet(get_complete_stylesheet())
    
    def setup_tyre_maintenance_tab(self):
        """Set up the tyre maintenance tab."""
        # Create tyre maintenance widget
        self.tyre_maintenance_widget = QWidget()
        tyre_layout = QVBoxLayout(self.tyre_maintenance_widget)
        tyre_layout.setContentsMargins(10, 10, 10, 10)
        
        # Create a splitter for the main content
        content_splitter = QSplitter(Qt.Horizontal)
        
        # Left side - Conditioning list
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        
        # Search and filter controls
        filter_layout = QHBoxLayout()
        
        # Search box
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Search equipment...")
        self.search_edit.textChanged.connect(self.filter_conditioning)
        filter_layout.addWidget(self.search_edit)
        
        # Equipment type filter
        self.equipment_type_filter = QComboBox()
        self.equipment_type_filter.addItem("All Types", None)
        self.equipment_type_filter.currentIndexChanged.connect(self.filter_conditioning)
        filter_layout.addWidget(self.equipment_type_filter)
        
        # Status filter
        self.status_filter = QComboBox()
        self.status_filter.addItem("All", None)
        self.status_filter.addItem("Pending", "pending")
        self.status_filter.addItem("Completed", "completed")
        self.status_filter.currentIndexChanged.connect(self.filter_conditioning)
        filter_layout.addWidget(self.status_filter)
        
        # Apply styling to fix hover issues
        from ui.common_styles import apply_combobox_hover_fix
        apply_combobox_hover_fix(self.equipment_type_filter)
        apply_combobox_hover_fix(self.status_filter)
        
        left_layout.addLayout(filter_layout)
        
        # Conditioning table
        self.conditioning_table = ReadOnlyTableWidget()
        self.conditioning_table.setColumnCount(5)  # Reduced from 6 to 5 (hiding quantity)
        self.conditioning_table.setHorizontalHeaderLabels(
            ["Equipment (BA No. & Make/Type)", "Date of Change", "Current KMs", "Rotation Interval", "Status"]
        )
        self.conditioning_table.verticalHeader().setVisible(False)
        self.conditioning_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.conditioning_table.setSelectionMode(QAbstractItemView.SingleSelection)  # Start with single selection
        self.conditioning_table.setSortingEnabled(True)
        self.conditioning_table.row_clicked.connect(self.conditioning_selected)
        
        # Connect selection changed signal to update button states
        self.conditioning_table.selectionModel().selectionChanged.connect(self.update_conditioning_button_states)
        
        # Add blue highlighting for selected rows
        self.conditioning_table.setStyleSheet("""
            QTableWidget::item:selected {
                background-color: #2196F3;
                color: white;
            }
            QTableWidget::item:hover {
                background-color: #E3F2FD;
            }
        """)
        
        left_layout.addWidget(self.conditioning_table)
        
        # Buttons
        button_layout = QHBoxLayout()
        apply_button_layout(button_layout)

        self.add_button = QPushButton("Add Conditioning")
        self.add_button.clicked.connect(self.add_conditioning)
        apply_button_style(self.add_button, "primary")
        button_layout.addWidget(self.add_button)

        self.edit_button = QPushButton("Edit Conditioning")
        self.edit_button.clicked.connect(self.edit_conditioning)
        self.edit_button.setEnabled(False)
        apply_button_style(self.edit_button, "default")
        button_layout.addWidget(self.edit_button)

        self.delete_button = QPushButton("Delete Conditioning")
        self.delete_button.clicked.connect(self.delete_conditioning)
        self.delete_button.setEnabled(False)
        apply_button_style(self.delete_button, "danger")
        button_layout.addWidget(self.delete_button)
        
        # Add separator
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setFrameShadow(QFrame.Sunken)
        button_layout.addWidget(separator)
        
        self.multiselect_button = QPushButton("Multi-Select")
        self.multiselect_button.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; }")
        self.multiselect_button.setCheckable(True)
        self.multiselect_button.clicked.connect(self.toggle_multiselect_conditioning)
        button_layout.addWidget(self.multiselect_button)
        
        self.deselect_all_button = QPushButton("Deselect All")
        self.deselect_all_button.setStyleSheet("QPushButton { background-color: #FF9800; color: white; font-weight: bold; }")
        self.deselect_all_button.clicked.connect(self.deselect_all_conditioning)
        self.deselect_all_button.setEnabled(False)
        button_layout.addWidget(self.deselect_all_button)
        
        self.create_demand_button = QPushButton("Create Demand")
        self.create_demand_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        self.create_demand_button.clicked.connect(self.create_tyre_demand)
        self.create_demand_button.setEnabled(False)
        button_layout.addWidget(self.create_demand_button)
        
        left_layout.addLayout(button_layout)
        
        # Right side - Conditioning details
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        
        # Conditioning details group
        details_group = QGroupBox("Tyre Maintenance Details")
        details_layout = QFormLayout(details_group)
        
        self.detail_equipment = QLabel("--")
        details_layout.addRow("Equipment:", self.detail_equipment)
        
        self.detail_date_of_change = QLabel("--")
        details_layout.addRow("Date of Change:", self.detail_date_of_change)
        
        self.detail_rotation_kms = QLabel("--")
        details_layout.addRow("Rotation Interval:", self.detail_rotation_kms)
        
        self.detail_condition_kms = QLabel("--")
        details_layout.addRow("Inspection KMs:", self.detail_condition_kms)
        
        self.detail_condition_years = QLabel("--")
        details_layout.addRow("Inspection Years:", self.detail_condition_years)
        
        right_layout.addWidget(details_group)
        
        # Equipment status group
        status_group = QGroupBox("Equipment Status")
        status_layout = QFormLayout(status_group)
        
        self.detail_make_type = QLabel("--")
        status_layout.addRow("Make & Type:", self.detail_make_type)
        
        self.detail_serial = QLabel("--")
        status_layout.addRow("Serial Number:", self.detail_serial)
        
        self.detail_current_kms = QLabel("--")
        status_layout.addRow("Current Meterage:", self.detail_current_kms)
        
        self.detail_next_rotation = QLabel("--")
        status_layout.addRow("Next Rotation Due:", self.detail_next_rotation)
        
        self.detail_rotation_status = StatusLabel("--")
        status_layout.addRow("Rotation Status:", self.detail_rotation_status)
        
        self.detail_inspection_status = StatusLabel("--")
        status_layout.addRow("Inspection Status:", self.detail_inspection_status)
        
        right_layout.addWidget(status_group)
        
        # Quick actions group
        actions_group = QGroupBox("Quick Actions")
        actions_layout = QVBoxLayout(actions_group)
        
        # Record rotation button
        self.record_rotation_button = QPushButton("Record Rotation")
        self.record_rotation_button.clicked.connect(self.record_rotation)
        self.record_rotation_button.setEnabled(False)
        actions_layout.addWidget(self.record_rotation_button)
        
        # Record inspection button
        self.record_inspection_button = QPushButton("Record Inspection")
        self.record_inspection_button.clicked.connect(self.record_inspection)
        self.record_inspection_button.setEnabled(False)
        actions_layout.addWidget(self.record_inspection_button)
        
        right_layout.addWidget(actions_group)
        
        # Tyre Maintenance History group
        history_group = QGroupBox("Tyre Maintenance History")
        history_layout = QVBoxLayout(history_group)
        
        self.history_table = ReadOnlyTableWidget()
        self.history_table.setColumnCount(4)
        self.history_table.setHorizontalHeaderLabels(
            ["Maintenance Type", "Done Date", "Meterage", "Notes"]
        )
        history_layout.addWidget(self.history_table)
        
        right_layout.addWidget(history_group)
        
        # Add widgets to splitter
        content_splitter.addWidget(left_widget)
        content_splitter.addWidget(right_widget)
        content_splitter.setSizes([int(self.width() * 0.5), int(self.width() * 0.5)])
        
        tyre_layout.addWidget(content_splitter)
    
    def setup_battery_tab(self):
        """Set up the battery management tab."""
        # Create battery widget
        self.battery_widget = QWidget()
        battery_layout = QVBoxLayout(self.battery_widget)
        battery_layout.setContentsMargins(10, 10, 10, 10)
        
        # Create a splitter for the battery content
        battery_splitter = QSplitter(Qt.Horizontal)
        
        # Left side - Battery list
        battery_left_widget = QWidget()
        battery_left_layout = QVBoxLayout(battery_left_widget)
        battery_left_layout.setContentsMargins(0, 0, 0, 0)
        
        # Battery search and filter controls
        battery_filter_layout = QHBoxLayout()
        
        # Battery search box
        self.battery_search_edit = QLineEdit()
        self.battery_search_edit.setPlaceholderText("Search equipment...")
        self.battery_search_edit.textChanged.connect(self.filter_battery)
        battery_filter_layout.addWidget(self.battery_search_edit)
        
        # Battery equipment type filter
        self.battery_equipment_type_filter = QComboBox()
        self.battery_equipment_type_filter.addItem("All Types", None)
        battery_filter_layout.addWidget(self.battery_equipment_type_filter)
        
        # Battery status filter
        self.battery_status_filter = QComboBox()
        self.battery_status_filter.addItems(["All Status", "Due for Inspection", "Normal"])
        self.battery_status_filter.currentIndexChanged.connect(self.filter_battery)
        battery_filter_layout.addWidget(self.battery_status_filter)
        
        # Apply styling to fix hover issues for battery dropdowns
        apply_combobox_hover_fix(self.battery_equipment_type_filter)
        apply_combobox_hover_fix(self.battery_status_filter)
        
        battery_left_layout.addLayout(battery_filter_layout)
        
        # Battery table
        self.battery_table = ReadOnlyTableWidget()
        self.battery_table.setColumnCount(6)
        self.battery_table.setHorizontalHeaderLabels(
            ["Equipment", "Done Date", "Due Date", "Life", "Age (Months)", "Status"]
        )
        self.battery_table.verticalHeader().setVisible(False)
        self.battery_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.battery_table.setSelectionMode(QAbstractItemView.SingleSelection)  # Start with single selection
        self.battery_table.setSortingEnabled(True)
        self.battery_table.row_clicked.connect(self.battery_selected)
        
        # Connect selection changed signal to update button states
        self.battery_table.selectionModel().selectionChanged.connect(self.update_battery_button_states)
        
        # Add blue highlighting for selected rows
        self.battery_table.setStyleSheet("""
            QTableWidget::item:selected {
                background-color: #2196F3;
                color: white;
            }
            QTableWidget::item:hover {
                background-color: #E3F2FD;
            }
        """)
        
        battery_left_layout.addWidget(self.battery_table)
        
        # Battery buttons
        battery_button_layout = QHBoxLayout()
        
        self.add_battery_button = QPushButton("Add Battery Record")
        self.add_battery_button.clicked.connect(self.add_battery)
        battery_button_layout.addWidget(self.add_battery_button)
        
        self.edit_battery_button = QPushButton("Edit Battery Record")
        self.edit_battery_button.clicked.connect(self.edit_battery)
        self.edit_battery_button.setEnabled(False)
        battery_button_layout.addWidget(self.edit_battery_button)
        
        self.delete_battery_button = QPushButton("Delete Battery Record")
        self.delete_battery_button.setObjectName("deleteButton")
        self.delete_battery_button.clicked.connect(self.delete_battery)
        self.delete_battery_button.setEnabled(False)
        battery_button_layout.addWidget(self.delete_battery_button)
        
        # Add separator
        battery_separator = QFrame()
        battery_separator.setFrameShape(QFrame.VLine)
        battery_separator.setFrameShadow(QFrame.Sunken)
        battery_button_layout.addWidget(battery_separator)
        
        self.multiselect_battery_button = QPushButton("Multi-Select")
        self.multiselect_battery_button.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; }")
        self.multiselect_battery_button.setCheckable(True)
        self.multiselect_battery_button.clicked.connect(self.toggle_multiselect_battery)
        battery_button_layout.addWidget(self.multiselect_battery_button)
        
        self.deselect_all_battery_button = QPushButton("Deselect All")
        self.deselect_all_battery_button.setStyleSheet("QPushButton { background-color: #FF9800; color: white; font-weight: bold; }")
        self.deselect_all_battery_button.clicked.connect(self.deselect_all_battery)
        self.deselect_all_battery_button.setEnabled(False)
        battery_button_layout.addWidget(self.deselect_all_battery_button)
        
        self.create_battery_demand_button = QPushButton("Create Demand")
        self.create_battery_demand_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        self.create_battery_demand_button.clicked.connect(self.create_battery_demand)
        self.create_battery_demand_button.setEnabled(False)
        battery_button_layout.addWidget(self.create_battery_demand_button)
        
        battery_left_layout.addLayout(battery_button_layout)
        
        # Right side - Battery details
        battery_right_widget = QWidget()
        battery_right_layout = QVBoxLayout(battery_right_widget)
        battery_right_layout.setContentsMargins(0, 0, 0, 0)
        
        # Battery details group
        battery_details_group = QGroupBox("Battery Details")
        battery_details_layout = QFormLayout(battery_details_group)
        
        self.battery_detail_equipment = QLabel("--")
        battery_details_layout.addRow("Equipment:", self.battery_detail_equipment)
        
        self.battery_detail_done_date = QLabel("--")
        battery_details_layout.addRow("Done Date:", self.battery_detail_done_date)
        
        self.battery_detail_due_date = QLabel("--")
        battery_details_layout.addRow("Due Date:", self.battery_detail_due_date)
        
        self.battery_detail_age = QLabel("--")
        battery_details_layout.addRow("Age (Months):", self.battery_detail_age)
        
        self.battery_detail_life = QLabel("--")
        battery_details_layout.addRow("Life:", self.battery_detail_life)
        
        self.battery_detail_status = StatusLabel("--")
        battery_details_layout.addRow("Status:", self.battery_detail_status)
        
        battery_right_layout.addWidget(battery_details_group)
        
        # Add widgets to splitter
        battery_splitter.addWidget(battery_left_widget)
        battery_splitter.addWidget(battery_right_widget)
        battery_splitter.setSizes([int(self.width() * 0.5), int(self.width() * 0.5)])
        
        battery_layout.addWidget(battery_splitter)
    
    def load_data(self):
        """Load conditioning data into the tables."""
        # Load tyre maintenance data
        self.load_tyre_maintenance_data()
        
        # Load battery data
        self.load_battery_data()
    
    def load_tyre_maintenance_data(self):
        """Load tyre maintenance data into the table."""
        # Get all conditioning records
        conditioning_list = models.TyreMaintenance.get_all()
        
        # Load equipment type filter
        self.load_equipment_type_filter(conditioning_list)
        
        # Clear table
        self.conditioning_table.setRowCount(0)
        
        # Add data to table
        for row, conditioning in enumerate(conditioning_list):
            self.conditioning_table.insertRow(row)
            
            # Equipment BA number and make/type (Column 1)
            ba_number = conditioning.get('ba_number') or conditioning.get('BaNumber') or ''
            make_type = conditioning.get('make_and_type') or conditioning.get('MakeAndType') or ''
            equipment_text = f"{ba_number} - {make_type}" if ba_number else make_type
            
            item = QTableWidgetItem(equipment_text)
            item.setData(Qt.UserRole, conditioning.get('tyre_maintenance_id') or conditioning.get('TyreMaintenanceID'))
            # Also store equipment ID and type for filtering
            item.setData(Qt.UserRole + 1, conditioning.get('equipment_id') or conditioning.get('EquipmentID'))
            item.setData(Qt.UserRole + 2, (make_type.split()[0] if make_type else ''))  # First word as type
            self.conditioning_table.setItem(row, 0, item)
            
            # Date of Change (Column 2)
            date_of_change = conditioning.get('date_of_change') or conditioning.get('DateOfChange') or conditioning.get('last_rotation_date') or conditioning.get('LastRotationDate') or "--"
            self.conditioning_table.setItem(row, 1, QTableWidgetItem(str(date_of_change)))
            
            # Rotation (Column 3) - Show current equipment KMs
            current_kms_val = conditioning.get('MeterageKMs') or conditioning.get('meterage_kms') or 0
            current_kms_display = f"{current_kms_val:,.0f} KM" if current_kms_val > 0 else "0 KM"
            self.conditioning_table.setItem(row, 2, QTableWidgetItem(current_kms_display))
            
            # Tyre Condition (Column 4) - Show rotation interval (default 5000 KM)
            rotation_interval = conditioning.get('TyreRotationKMs') or conditioning.get('tyre_rotation_kms') or 5000
            condition_display = f"{rotation_interval:,} KM" if rotation_interval > 0 else "5,000 KM"
            self.conditioning_table.setItem(row, 3, QTableWidgetItem(condition_display))
            
            # Status (Column 5) - Enhanced status with more variations
            status = "Normal"
            status_color = QColor("#4CAF50")  # Green
            
            current_meterage = float(current_kms_val or 0)
            rotation_threshold = float(rotation_interval or 5000)
            
            if current_meterage > 0 and rotation_threshold > 0:
                # Calculate next rotation milestone based on rotation interval
                import math
                next_rotation_milestone = math.ceil(current_meterage / rotation_threshold) * rotation_threshold
                
                # If current meterage equals or exceeds the next milestone, due for rotation
                if current_meterage >= next_rotation_milestone:
                    status = "Due for Rotation"
                    status_color = QColor("#F44336")  # Red
                else:
                    # Calculate percentage complete for enhanced status
                    percentage_complete = (current_meterage % rotation_threshold) / rotation_threshold
                    
                    if percentage_complete >= 0.90:  # 90% or more
                        status = "Rotation Warning"
                        status_color = QColor("#FF9800")  # Orange
                    elif percentage_complete >= 0.80:  # 80% or more
                        status = "Tyre Rotation Incoming"
                        status_color = QColor("#2196F3")  # Blue
                    else:
                        status = "Normal"
                        status_color = QColor("#4CAF50")  # Green
            
            status_item = QTableWidgetItem(status)
            status_item.setForeground(status_color)
            status_item.setData(Qt.UserRole, status)  # Store status for filtering
            self.conditioning_table.setItem(row, 4, status_item)  # Changed from column 5 to 4
        
        # Resize columns to content
        self.conditioning_table.resizeColumnsToContents()
        
        # Clear details
        self.clear_conditioning_details()
        
        # Update button states
        self.update_conditioning_button_states()
    
    def load_equipment_type_filter(self, conditioning_list):
        """Load equipment type filter dropdown."""
        # Clear existing items
        self.equipment_type_filter.clear()
        self.equipment_type_filter.addItem("All Types", None)
        
        # Get unique equipment types
        equipment_types = set()
        for conditioning in conditioning_list:
            make_type = conditioning.get('make_and_type') or conditioning.get('MakeAndType') or ''
            equipment_type = make_type.split()[0] if make_type else ''  # First word as type
            equipment_types.add(equipment_type)
        
        # Add to filter
        for equipment_type in sorted(equipment_types):
            self.equipment_type_filter.addItem(equipment_type, equipment_type)
    
    def filter_conditioning(self):
        """Filter conditioning based on search text, equipment type, and status."""
        search_text = self.search_edit.text().lower()
        equipment_type_filter = self.equipment_type_filter.currentData()
        status_index = self.status_filter.currentIndex()
        
        for row in range(self.conditioning_table.rowCount()):
            show_row = True
            
            # Check search text (equipment name)
            if search_text:
                equipment_item = self.conditioning_table.item(row, 0)
                if equipment_item and search_text not in equipment_item.text().lower():
                    show_row = False
            
            # Check equipment type filter
            if show_row and equipment_type_filter:
                equipment_item = self.conditioning_table.item(row, 0)
                if equipment_item:
                    stored_type = equipment_item.data(Qt.UserRole + 2)
                    if stored_type != equipment_type_filter:
                        show_row = False
            
            # Check status filter
            if show_row and status_index > 0:
                status_item = self.conditioning_table.item(row, 4)  # Changed from column 5 to 4
                if status_item:
                    status = status_item.text()
                    if status_index == 1 and "Rotation" not in status:  # Due for Rotation
                        show_row = False
                    elif status_index == 2 and status != "Normal":  # Normal
                        show_row = False
            
            # Show or hide row
            self.conditioning_table.setRowHidden(row, not show_row)
    
    def conditioning_selected(self, row):
        """Handle conditioning row selection."""
        selected_rows = [index.row() for index in self.conditioning_table.selectedIndexes()]
        unique_rows = list(set(selected_rows))  # Remove duplicates
        
        # Update Deselect All button state
        if unique_rows:
            self.deselect_all_button.setEnabled(True)
        else:
            self.deselect_all_button.setEnabled(False)
        
        if not unique_rows:
            self.edit_button.setEnabled(False)
            self.delete_button.setEnabled(False)
            self.record_rotation_button.setEnabled(False)
            self.record_inspection_button.setEnabled(False)
            self.create_demand_button.setEnabled(False)
            self.clear_conditioning_details()
            return
        elif len(unique_rows) == 1:
            # Single selection - enable all buttons and show details
            self.edit_button.setEnabled(True)
            self.delete_button.setEnabled(True)
            self.record_rotation_button.setEnabled(True)
            self.record_inspection_button.setEnabled(True)
            self.create_demand_button.setEnabled(True)
            
            # Show details for the selected item
            conditioning_id = self.conditioning_table.item(unique_rows[0], 0).data(Qt.UserRole)
            
            # Get conditioning details
            conditioning = models.TyreMaintenance.get_by_id(conditioning_id)
            if not conditioning:
                self.clear_conditioning_details()
                return
            
            # Update details display
            equipment_display = f"{conditioning.get('ba_number', '')} - {conditioning.get('make_and_type', '')}"
            if not conditioning.get('ba_number'):
                equipment_display = conditioning.get('make_and_type', '')
            self.detail_equipment.setText(equipment_display)
            
            self.detail_date_of_change.setText(conditioning.get('date_of_change') or conditioning.get('last_rotation_date') or "--")
            
            rotation_kms = conditioning.get('tyre_rotation_kms') or conditioning.get('TyreRotationKMs') or 5000
            self.detail_rotation_kms.setText(f"{rotation_kms:,} KM")
            
            condition_kms = conditioning.get('tyre_condition_kms') or conditioning.get('TyreConditionKMs') or 0
            self.detail_condition_kms.setText(f"{condition_kms:,} KM" if condition_kms > 0 else "Not Set")
            
            condition_years = conditioning.get('tyre_condition_years') or conditioning.get('TyreConditionYears') or 0
            self.detail_condition_years.setText(f"{condition_years} Years" if condition_years > 0 else "Not Set")
            
            # Update equipment status
            self.detail_make_type.setText(conditioning.get('make_and_type', '--'))
            self.detail_serial.setText(str(conditioning.get('serial_number', '--')))
            
            current_kms = conditioning.get('MeterageKMs') or conditioning.get('meterage_kms') or 0
            self.detail_current_kms.setText(f"{current_kms:,.0f} KM")
            
            # Calculate next rotation due with enhanced status variations
            if rotation_kms > 0 and current_kms > 0:
                import math
                next_rotation_milestone = math.ceil(current_kms / rotation_kms) * rotation_kms
                self.detail_next_rotation.setText(f"{next_rotation_milestone:,.0f} KM")
                
                # Enhanced rotation status with more variations
                if current_kms >= next_rotation_milestone:
                    self.detail_rotation_status.setText("Due for Rotation")
                    self.detail_rotation_status.setStatus("error")
                else:
                    remaining_kms = next_rotation_milestone - current_kms
                    percentage_complete = (current_kms % rotation_kms) / rotation_kms
                    
                    if percentage_complete >= 0.90:  # 90% or more
                        self.detail_rotation_status.setText("Rotation Warning")
                        self.detail_rotation_status.setStatus("warning")
                    elif percentage_complete >= 0.80:  # 80% or more
                        self.detail_rotation_status.setText("Tyre Rotation Incoming")
                        self.detail_rotation_status.setStatus("info")
                    else:
                        self.detail_rotation_status.setText("Normal")
                        self.detail_rotation_status.setStatus("normal")
            else:
                self.detail_next_rotation.setText("--")
                self.detail_rotation_status.setText("--")
                self.detail_rotation_status.setStatus("normal")
            
            # Add inspection status calculation with same logic
            self.detail_inspection_status = getattr(self, 'detail_inspection_status', StatusLabel("--"))
            if condition_kms > 0 and current_kms > 0:
                # Calculate inspection status similar to rotation
                percentage_complete = current_kms / condition_kms
                
                if current_kms >= condition_kms:
                    self.detail_inspection_status.setText("Due for Inspection")
                    self.detail_inspection_status.setStatus("error")
                elif percentage_complete >= 0.90:  # 90% or more
                    self.detail_inspection_status.setText("Inspection Warning")
                    self.detail_inspection_status.setStatus("warning")
                elif percentage_complete >= 0.80:  # 80% or more
                    self.detail_inspection_status.setText("Inspection Incoming")
                    self.detail_inspection_status.setStatus("info")
                else:
                    self.detail_inspection_status.setText("Normal")
                    self.detail_inspection_status.setStatus("normal")
            elif condition_years > 0:
                # Check years-based inspection
                vintage_years = conditioning.get('VintageYears') or conditioning.get('vintage_years') or 0
                if vintage_years >= condition_years:
                    self.detail_inspection_status.setText("Due for Inspection")
                    self.detail_inspection_status.setStatus("error")
                elif vintage_years >= condition_years * 0.90:
                    self.detail_inspection_status.setText("Inspection Warning")
                    self.detail_inspection_status.setStatus("warning")
                elif vintage_years >= condition_years * 0.80:
                    self.detail_inspection_status.setText("Inspection Incoming")
                    self.detail_inspection_status.setStatus("info")
                else:
                    self.detail_inspection_status.setText("Normal")
                    self.detail_inspection_status.setStatus("normal")
            else:
                self.detail_inspection_status.setText("Not Set")
                self.detail_inspection_status.setStatus("normal")
            
            # Load equipment maintenance history
            equipment_id = conditioning.get('equipment_id') or conditioning.get('EquipmentID')
            if equipment_id:
                self.load_conditioning_history(equipment_id)
        else:
            # Multiple selection - enable only bulk operations
            self.edit_button.setEnabled(False)  # Can't edit multiple items
            self.delete_button.setEnabled(True)  # Can delete multiple items
            self.record_rotation_button.setEnabled(True)  # Can record rotation for multiple items
            self.record_inspection_button.setEnabled(True)  # Can record inspection for multiple items
            self.create_demand_button.setEnabled(True)  # Can create demand for multiple items
            
            # Show summary in details
            self.detail_equipment.setText(f"{len(unique_rows)} items selected")
            self.detail_date_of_change.setText("Multiple")
            self.detail_rotation_kms.setText("Multiple")
            self.detail_condition_kms.setText("Multiple")
            self.detail_condition_years.setText("Multiple")
            self.detail_make_type.setText("Multiple")
            self.detail_serial.setText("Multiple")
            self.detail_current_kms.setText("Multiple")
            self.detail_next_rotation.setText("Multiple")
            self.detail_rotation_status.setText("Multiple")
            self.detail_rotation_status.setStatus("normal")
            
            # Clear history table
            self.history_table.setRowCount(0)
    
    def load_conditioning_history(self, equipment_id):
        """Load conditioning history with enhanced tracking."""
        try:
            # Get enhanced conditioning history 
            history_records = models.ConditioningHistory.get_by_equipment(equipment_id)
            
            # Get traditional maintenance records for backwards compatibility
            traditional_records = []
            with database.get_db_connection() as conn:
                traditional_records = conn.execute('''
                    SELECT * FROM maintenance
                    WHERE equipment_id = ? AND
                          (maintenance_type = 'Conditioning Rotation' OR
                           maintenance_type = 'Conditioning Inspection' OR
                           maintenance_type LIKE '%Tyre%' OR
                           maintenance_type LIKE '%Conditioning%')
                    ORDER BY done_date DESC
                ''', (equipment_id,)).fetchall()

            # Combine records
            all_records = []
            
            # Add enhanced records
            for record in history_records or []:
                all_records.append({
                    'type': 'enhanced',
                    'date': record.get('action_date', ''),
                    'time': record.get('action_time', 'N/A'),
                    'action': record.get('action_type', '').title(),
                    'kms': f"{record.get('action_kms', 0):.1f}",
                    'details': f"{record.get('notes', 'No details')}",
                })
            
            # Add traditional records
            for record in traditional_records or []:
                all_records.append({
                    'type': 'traditional',
                    'date': record.get('done_date', ''),
                    'time': 'N/A',
                    'action': record.get('maintenance_type', ''),
                    'kms': 'N/A',
                    'details': record.get('completion_notes', 'Legacy maintenance record'),
                })
            
            # Sort by date and time (newest first)
            all_records.sort(key=lambda x: (x['date'], x['time']), reverse=True)
            
            # Update table
            self.history_table.setRowCount(len(all_records))
            self.history_table.setColumnCount(6)
            self.history_table.setHorizontalHeaderLabels([
                "Date", "Time", "Action", "KMs", "Details", "Source"
            ])
            
            for i, record in enumerate(all_records):
                self.history_table.setItem(i, 0, QTableWidgetItem(record['date']))
                self.history_table.setItem(i, 1, QTableWidgetItem(record['time']))
                self.history_table.setItem(i, 2, QTableWidgetItem(record['action']))
                self.history_table.setItem(i, 3, QTableWidgetItem(record['kms']))
                self.history_table.setItem(i, 4, QTableWidgetItem(record['details']))
                
                source_item = QTableWidgetItem("Enhanced" if record['type'] == 'enhanced' else "Legacy")
                if record['type'] == 'enhanced':
                    source_item.setBackground(QColor(200, 255, 200))  # Light green
                else:
                    source_item.setBackground(QColor(255, 255, 200))  # Light yellow
                self.history_table.setItem(i, 5, source_item)
            
            self.history_table.resizeColumnsToContents()
            
        except Exception as e:
            logger.error(f"Error loading conditioning history: {e}")
            self.history_table.setRowCount(0)
    
    def clear_conditioning_details(self):
        """Clear conditioning details."""
        self.detail_equipment.setText("--")
        self.detail_date_of_change.setText("--")
        self.detail_rotation_kms.setText("--")
        self.detail_condition_kms.setText("--")
        self.detail_condition_years.setText("--")
        
        self.detail_make_type.setText("--")
        self.detail_serial.setText("--")
        self.detail_current_kms.setText("--")
        self.detail_next_rotation.setText("--")
        self.detail_rotation_status.setText("--")
        self.detail_rotation_status.setStatus("normal")
        
        # Clear inspection status
        if hasattr(self, 'detail_inspection_status'):
            self.detail_inspection_status.setText("--")
            self.detail_inspection_status.setStatus("normal")
        
        # Clear history table
        self.history_table.setRowCount(0)
        
        # Disable buttons
        self.edit_button.setEnabled(False)
        self.delete_button.setEnabled(False)
        self.record_rotation_button.setEnabled(False)
        self.record_inspection_button.setEnabled(False)
    
    def add_conditioning(self):
        """Add new conditioning record."""
        # Get equipment list for dialog
        equipment_list = models.Equipment.get_active()
        
        dialog = ConditioningDialog(equipment_list=equipment_list, parent=self)
        if dialog.exec_():
            conditioning_data = dialog.get_tyre_maintenance_data()
            
            # Create conditioning model
            conditioning = models.TyreMaintenance(
                equipment_id=conditioning_data['EquipmentID'],
                tyre_rotation_kms=conditioning_data['TyreRotationKMs'],
                tyre_condition_kms=conditioning_data['TyreConditionKMs'],
                tyre_condition_years=conditioning_data['TyreConditionYears'],
                last_rotation_date=conditioning_data['LastRotationDate'],
                date_of_change=conditioning_data['DateOfChange']
            )
            
            try:
                # Save to database
                conditioning.save()
                
                # Reload data
                self.load_data()
                
                QMessageBox.information(self, "Success", 
                                      "Conditioning record added successfully.")
            except Exception as e:
                QMessageBox.critical(self, "Error", 
                                   f"Error adding conditioning record: {str(e)}")
    
    def edit_conditioning(self):
        """Edit selected conditioning record."""
        selected_row = self.conditioning_table.currentRow()
        if selected_row < 0:
            return
        
        # Get conditioning ID
        conditioning_id = self.conditioning_table.item(selected_row, 0).data(Qt.UserRole)
        
        # Get conditioning details
        conditioning = models.TyreMaintenance.get_by_id(conditioning_id)
        if not conditioning:
            return
        
        # Get equipment list for dialog
        equipment_list = models.Equipment.get_active()
        
        # Show dialog
        dialog = ConditioningDialog(conditioning, equipment_list, parent=self)
        if dialog.exec_():
            conditioning_data = dialog.get_tyre_maintenance_data()
            
            # Update conditioning model
            conditioning_model = models.TyreMaintenance(
                tyre_maintenance_id=conditioning_id,
                equipment_id=conditioning_data['EquipmentID'],
                tyre_rotation_kms=conditioning_data['TyreRotationKMs'],
                tyre_condition_kms=conditioning_data['TyreConditionKMs'],
                tyre_condition_years=conditioning_data['TyreConditionYears'],
                last_rotation_date=conditioning_data['LastRotationDate'],
                date_of_change=conditioning_data['DateOfChange']
            )
            
            try:
                # Save to database
                conditioning_model.save()
                
                # Reload data
                self.load_data()
                
                QMessageBox.information(self, "Success", 
                                      "Conditioning record updated successfully.")
            except Exception as e:
                QMessageBox.critical(self, "Error", 
                                   f"Error updating conditioning record: {str(e)}")
    
    def delete_conditioning(self):
        """Delete selected conditioning record."""
        selected_rows = self.conditioning_table.selectedIndexes()
        if not selected_rows:
            return
        
        conditioning_ids = [self.conditioning_table.item(index.row(), 0).data(Qt.UserRole) for index in selected_rows]
        
        for conditioning_id in conditioning_ids:
            conditioning = models.TyreMaintenance.get_by_id(conditioning_id)
            if not conditioning:
                continue
            
            # Get equipment details
            equipment = models.Equipment.get_by_id(conditioning.get('equipment_id') or conditioning.get('EquipmentID'))
            
            # Confirm deletion
            confirm = QMessageBox.question(
                self, "Confirm Deletion", 
                f"Are you sure you want to delete conditioning record for {equipment.make_and_type}?",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )
            
            if confirm != QMessageBox.Yes:
                continue
            
            try:
                # Delete from database
                models.TyreMaintenance.delete(conditioning_id)
                
                # Reload data
                self.load_data()
                
                QMessageBox.information(self, "Success", 
                                      "Conditioning record deleted successfully.")
            except Exception as e:
                QMessageBox.critical(self, "Error", 
                                   f"Error deleting conditioning record: {str(e)}")
    
    def record_rotation(self):
        """Record tyre rotation with enhanced historical tracking."""
        current_row = self.conditioning_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "No Selection", 
                              "Please select a conditioning record to record rotation.")
            return

        conditioning_id = self.conditioning_table.item(current_row, 0).data(Qt.UserRole)
        if not conditioning_id:
            QMessageBox.warning(self, "Invalid Selection", "Selected record is invalid.")
            return

        conditioning = models.TyreMaintenance.get_by_id(conditioning_id)
        equipment = models.Equipment.get_by_id(conditioning.get('equipment_id'))
        
        if not conditioning or not equipment:
            QMessageBox.warning(self, "Error", "Record not found.")
            return

        try:
            # Create historical record BEFORE updating
            from datetime import datetime
            today = date.today().isoformat()
            now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')  # Precise time down to seconds
            old_rotation_date = conditioning.get('last_rotation_date', 'Never')
            
            history = models.ConditioningHistory(
                equipment_id=equipment.equipment_id,
                action_type='rotation',
                action_date=today,
                action_kms=equipment.meterage_kms or 0.0,
                action_time=now,
                vintage_at_action=equipment.vintage_years or 0.0,
                performed_by="System User",
                notes=f"Tyre rotation recorded. Previous rotation: {old_rotation_date}. Interval reset.",
                old_rotation_date=old_rotation_date,
                new_rotation_date=today,
                interval_reset=True
            )
            
            history_id = history.save()
            if not history_id:
                raise Exception("Failed to save historical record")

            # Update conditioning record - Reset rotation interval
            conditioning_model = models.TyreMaintenance(
                tyre_maintenance_id=conditioning_id,
                equipment_id=conditioning.get('equipment_id'),
                tyre_rotation_kms=conditioning.get('tyre_rotation_kms', 5000),
                tyre_condition_kms=conditioning.get('tyre_condition_kms', 30000),
                tyre_condition_years=conditioning.get('tyre_condition_years', 2),
                last_rotation_date=today,
                date_of_change=today,
                quantity=conditioning.get('quantity', 0)
            )
            
            if not conditioning_model.save():
                raise Exception("Failed to update conditioning record")

            # Success message
            QMessageBox.information(
                self, "Success", 
                f"✅ Rotation recorded for {equipment.make_and_type or 'Equipment'}!\n\n"
                f"📅 Date: {today}\n"
                f"⏰ Time: {now}\n"
                f"📊 KMs: {equipment.meterage_kms or 0}\n"
                f"🔄 Interval reset: Next rotation due at +{conditioning.get('tyre_rotation_kms', 5000)} KM\n"
                f"📝 Historical record created (ID: {history_id})\n\n"
                f"The rotation interval has been reset to track from today's date."
            )

            # Refresh and maintain selection
            self.load_data()
            for row in range(self.conditioning_table.rowCount()):
                item = self.conditioning_table.item(row, 0)
                if item and item.data(Qt.UserRole) == conditioning_id:
                    self.conditioning_table.selectRow(row)
                    self.conditioning_selected(row)
                    break
                
        except Exception as e:
            logger.error(f"Error recording rotation: {e}")
            QMessageBox.critical(self, "Error", f"Failed to record rotation:\n{str(e)}")
    
    def record_inspection(self):
        """Record tyre inspection with enhanced historical tracking."""
        current_row = self.conditioning_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "No Selection", 
                              "Please select a conditioning record to record inspection.")
            return

        conditioning_id = self.conditioning_table.item(current_row, 0).data(Qt.UserRole)
        conditioning = models.TyreMaintenance.get_by_id(conditioning_id)
        equipment = models.Equipment.get_by_id(conditioning.get('equipment_id'))
        
        if not conditioning or not equipment:
            QMessageBox.warning(self, "Error", "Record not found.")
            return

        try:
            # Create historical record for inspection
            from datetime import datetime
            today = date.today().isoformat()
            now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')  # Precise time down to seconds
            
            history = models.ConditioningHistory(
                equipment_id=equipment.equipment_id,
                action_type='inspection',
                action_date=today,
                action_kms=equipment.meterage_kms or 0.0,
                action_time=now,
                vintage_at_action=equipment.vintage_years or 0.0,
                performed_by="System User",
                notes=f"Tyre condition inspection performed. Equipment at {equipment.meterage_kms or 0} KM.",
                old_rotation_date=conditioning.get('last_rotation_date'),
                new_rotation_date=conditioning.get('last_rotation_date'),
                interval_reset=False
            )
            
            history_id = history.save()
            if not history_id:
                raise Exception("Failed to save inspection historical record")

            # Ask if user wants to create demand for tyres
            create_demand = QMessageBox.question(
                self, "Create Demand?", 
                f"Inspection recorded for {equipment.make_and_type or 'Equipment'}.\n\n"
                f"Would you like to create a tyre demand based on this inspection?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            demand_msg = ""
            if create_demand == QMessageBox.Yes:
                # Use the existing create_tyre_demand functionality
                # First select the equipment row
                for row in range(self.conditioning_table.rowCount()):
                    item = self.conditioning_table.item(row, 0)
                    if item and item.data(Qt.UserRole) == conditioning_id:
                        self.conditioning_table.selectRow(row)
                        self.conditioning_selected(row)
                        break
                
                # Call the existing create demand method which opens the proper dialog
                self.create_tyre_demand()
                demand_msg = f"\n🎯 Demand creation dialog opened"
            else:
                demand_msg = f"\n📝 Inspection logged without demand creation"

            # Success message
            QMessageBox.information(
                self, "Success", 
                f"✅ Inspection recorded for {equipment.make_and_type or 'Equipment'}!\n\n"
                f"📅 Date: {today}\n"
                f"⏰ Time: {now}\n"
                f"📊 KMs: {equipment.meterage_kms or 0}\n"
                f"📝 Historical record created (ID: {history_id}){demand_msg}\n\n"
                f"The inspection has been logged for future reference and analysis."
            )

            # Refresh and maintain selection
            self.load_data()
            for row in range(self.conditioning_table.rowCount()):
                item = self.conditioning_table.item(row, 0)
                if item and item.data(Qt.UserRole) == conditioning_id:
                    self.conditioning_table.selectRow(row)
                    break

        except Exception as e:
            logger.error(f"Error recording inspection: {e}")
            QMessageBox.critical(self, "Error", f"Failed to record inspection:\n{str(e)}")
    
    def select_conditioning(self, conditioning_id):
        """Select conditioning with the given ID."""
        for row in range(self.conditioning_table.rowCount()):
            item = self.conditioning_table.item(row, 0)
            if item and item.data(Qt.UserRole) == conditioning_id:
                self.conditioning_table.selectRow(row)
                self.conditioning_selected(row)
                break
    
    # Battery Management Methods
    def load_battery_data(self):
        """Load battery data into the battery table."""
        from datetime import datetime, timedelta

        # Get all battery records - handle database schema issues gracefully
        try:
            battery_list = models.Battery.get_all()
        except Exception as e:
            logger.warning(f"Could not load battery data (database schema issue): {e}")
            battery_list = []
        
        # Clear table
        self.battery_table.setRowCount(0)
        
        # Add data to table
        for row, battery in enumerate(battery_list):
            self.battery_table.insertRow(row)
            
            # Equipment
            equipment_text = utils.format_equipment_display(battery)
            item = QTableWidgetItem(equipment_text)
            item.setData(Qt.UserRole, battery.get('battery_id'))
            item.setData(Qt.UserRole + 1, battery.get('equipment_id'))
            self.battery_table.setItem(row, 0, item)
            
            # Done Date
            done_date = battery.get('done_date', '')
            self.battery_table.setItem(row, 1, QTableWidgetItem(str(done_date)))
            
            # Calculate due date and age
            if done_date:
                try:
                    done_dt = datetime.strptime(str(done_date), '%Y-%m-%d')
                    
                    # Get custom life or default to 24 months (2 years)
                    life_months = battery.get('custom_life_months') or 24
                    
                    # Calculate due date
                    due_dt = done_dt + timedelta(days=life_months * 30.44)  # Average month length
                    due_date_str = due_dt.strftime('%Y-%m-%d')
                    
                    # Calculate age in months
                    today = datetime.now()
                    age_months = int((today - done_dt).days / 30.44)
                    
                    # Calculate status
                    days_until_due = (due_dt - today).days
                    if days_until_due <= 30:
                        status = "Due for Inspection"
                        status_color = QColor("#F44336")  # Red
                    else:
                        status = "Normal"
                        status_color = QColor("#4CAF50")  # Green
                    
                except ValueError:
                    due_date_str = "--"
                    age_months = 0
                    life_months = 24  # Default
                    status = "Unknown"
                    status_color = QColor("#9E9E9E")  # Gray
            else:
                due_date_str = "--"
                age_months = 0
                life_months = battery.get('custom_life_months') or 24
                status = "Unknown"
                status_color = QColor("#9E9E9E")  # Gray
            
            # Due Date
            self.battery_table.setItem(row, 2, QTableWidgetItem(due_date_str))
            
            # Life (in months)
            life_display = f"{life_months} months" if life_months != 24 else "24 months (default)"
            self.battery_table.setItem(row, 3, QTableWidgetItem(life_display))
            
            # Age (Months)
            self.battery_table.setItem(row, 4, QTableWidgetItem(str(age_months)))
            
            # Status
            status_item = QTableWidgetItem(status)
            status_item.setForeground(status_color)
            status_item.setData(Qt.UserRole, status)
            self.battery_table.setItem(row, 5, status_item)
        
        # Resize columns to content
        if not selected_rows:
            self.record_inspection_button.setEnabled(True)  # Re-enable if no selection
            return
        
        # Get unique row numbers only (selectedIndexes returns all cells in selected rows)
        unique_rows = list(set([index.row() for index in selected_rows]))
        conditioning_ids = [self.conditioning_table.item(row, 0).data(Qt.UserRole) for row in unique_rows]
        
        for conditioning_id in conditioning_ids:
            conditioning = models.TyreMaintenance.get_by_id(conditioning_id)
            if not conditioning:
                continue
            
            # Get equipment details
            equipment = models.Equipment.get_by_id(conditioning.get('equipment_id') or conditioning.get('EquipmentID'))
            if not equipment:
                continue
            
            # Confirm action
            confirm = QMessageBox.question(
                self, "Record Inspection", 
                f"Record an inspection for {equipment.make_and_type}?",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )
            
            if confirm != QMessageBox.Yes:
                continue
            
            try:
                # Record inspection as historical record only (no maintenance record created)
                today = date.today().isoformat()
                
                # Log the inspection action as historical record
                logger.info(f"Conditioning inspection recorded for equipment {equipment.equipment_id} ({equipment.make_and_type}) on {today}")
                
                # Ask if user wants to create demand for tyres
                create_demand = QMessageBox.question(
                    self, "Create Demand?", 
                    f"Inspection recorded for {equipment.make_and_type}.\n\nWould you like to create a demand for tyres now?",
                    QMessageBox.Yes | QMessageBox.No, QMessageBox.No
                )
                
                if create_demand == QMessageBox.Yes:
                    # Use the existing create_tyre_demand functionality
                    # First select the equipment row
                    for row in range(self.conditioning_table.rowCount()):
                        item = self.conditioning_table.item(row, 0)
                        if item and item.data(Qt.UserRole) == conditioning_id:
                            self.conditioning_table.selectRow(row)
                            self.conditioning_selected(row)
                            break
                    
                    # Call the existing create demand method which opens the proper dialog
                    self.create_tyre_demand()
                    demand_msg = f"\n🎯 Demand creation dialog opened"
                else:
                    demand_msg = f"\n📝 Inspection logged without demand creation"

                # Success message
                QMessageBox.information(
                    self, "Success", 
                    f"✅ Inspection recorded for {equipment.make_and_type or 'Equipment'}!\n\n"
                    f"📅 Date: {today}\n"
                    f"⏰ Time: {now}\n"
                    f"📊 KMs: {equipment.meterage_kms or 0}\n"
                    f"📝 Historical record created (ID: {history_id}){demand_msg}\n\n"
                    f"The inspection has been logged for future reference and analysis."
                )
                
                # Reload data to refresh display
                self.load_data()
                
                # Reselect the row to show updated details
                for row in range(self.conditioning_table.rowCount()):
                    item = self.conditioning_table.item(row, 0)
                    if item and item.data(Qt.UserRole) == conditioning_id:
                        self.conditioning_table.selectRow(row)
                        self.conditioning_selected(row)
                        break
                
                QMessageBox.information(self, "Success", 
                                      "Inspection recorded successfully as historical record.")
            except Exception as e:
                QMessageBox.critical(self, "Error", 
                                   f"Error recording inspection: {str(e)}")
        
        # Re-enable button after all operations complete
        self.record_inspection_button.setEnabled(True)
    
    def select_conditioning(self, conditioning_id):
        """Select conditioning with the given ID."""
        for row in range(self.conditioning_table.rowCount()):
            item = self.conditioning_table.item(row, 0)
            if item and item.data(Qt.UserRole) == conditioning_id:
                self.conditioning_table.selectRow(row)
                self.conditioning_selected(row)
                break
    
    # Battery Management Methods
    def load_battery_data(self):
        """Load battery data into the battery table."""
        from datetime import datetime, timedelta

        # Get all battery records - handle database schema issues gracefully
        try:
            battery_list = models.Battery.get_all()
        except Exception as e:
            logger.warning(f"Could not load battery data (database schema issue): {e}")
            battery_list = []
        
        # Clear table
        self.battery_table.setRowCount(0)
        
        # Add data to table
        for row, battery in enumerate(battery_list):
            self.battery_table.insertRow(row)
            
            # Equipment
            equipment_text = utils.format_equipment_display(battery)
            item = QTableWidgetItem(equipment_text)
            item.setData(Qt.UserRole, battery.get('battery_id'))
            item.setData(Qt.UserRole + 1, battery.get('equipment_id'))
            self.battery_table.setItem(row, 0, item)
            
            # Done Date
            done_date = battery.get('done_date', '')
            self.battery_table.setItem(row, 1, QTableWidgetItem(str(done_date)))
            
            # Calculate due date and age
            if done_date:
                try:
                    done_dt = datetime.strptime(str(done_date), '%Y-%m-%d')
                    
                    # Get custom life or default to 24 months (2 years)
                    life_months = battery.get('custom_life_months') or 24
                    
                    # Calculate due date
                    due_dt = done_dt + timedelta(days=life_months * 30.44)  # Average month length
                    due_date_str = due_dt.strftime('%Y-%m-%d')
                    
                    # Calculate age in months
                    today = datetime.now()
                    age_months = int((today - done_dt).days / 30.44)
                    
                    # Calculate status
                    days_until_due = (due_dt - today).days
                    if days_until_due <= 30:
                        status = "Due for Inspection"
                        status_color = QColor("#F44336")  # Red
                    else:
                        status = "Normal"
                        status_color = QColor("#4CAF50")  # Green
                    
                except ValueError:
                    due_date_str = "--"
                    age_months = 0
                    life_months = 24  # Default
                    status = "Unknown"
                    status_color = QColor("#9E9E9E")  # Gray
            else:
                due_date_str = "--"
                age_months = 0
                life_months = battery.get('custom_life_months') or 24
                status = "Unknown"
                status_color = QColor("#9E9E9E")  # Gray
            
            # Due Date
            self.battery_table.setItem(row, 2, QTableWidgetItem(due_date_str))
            
            # Life (in months)
            life_display = f"{life_months} months" if life_months != 24 else "24 months (default)"
            self.battery_table.setItem(row, 3, QTableWidgetItem(life_display))
            
            # Age (Months)
            self.battery_table.setItem(row, 4, QTableWidgetItem(str(age_months)))
            
            # Status
            status_item = QTableWidgetItem(status)
            status_item.setForeground(status_color)
            status_item.setData(Qt.UserRole, status)
            self.battery_table.setItem(row, 5, status_item)
        
        # Resize columns to content
        self.battery_table.resizeColumnsToContents()
        
        # Clear battery details
        self.clear_battery_details()
        
        # Update button states
        self.update_battery_button_states()
    
    def filter_battery(self):
        """Filter battery list based on search text and filters."""
        search_text = self.battery_search_edit.text().lower()
        equipment_type = self.battery_equipment_type_filter.currentData()
        status_index = self.battery_status_filter.currentIndex()
        
        for row in range(self.battery_table.rowCount()):
            show_row = True
            
            # Check search text
            if search_text:
                row_text = ""
                for col in range(self.battery_table.columnCount()):
                    item = self.battery_table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "
                
                if search_text not in row_text:
                    show_row = False
            
            # Check status filter
            if status_index > 0:
                status_item = self.battery_table.item(row, 5)
                status = status_item.text() if status_item else ""
                
                if status_index == 1 and "Due for Inspection" not in status:
                    show_row = False
                elif status_index == 2 and status != "Normal":
                    show_row = False
            
            # Show or hide row
            self.battery_table.setRowHidden(row, not show_row)
    
    def battery_selected(self, row):
        """Handle battery row selection."""
        selected_rows = [index.row() for index in self.battery_table.selectedIndexes()]
        unique_rows = list(set(selected_rows))  # Remove duplicates
        
        # Update Deselect All button state
        if unique_rows:
            self.deselect_all_battery_button.setEnabled(True)
        else:
            self.deselect_all_battery_button.setEnabled(False)
        
        if not unique_rows:
            self.edit_battery_button.setEnabled(False)
            self.delete_battery_button.setEnabled(False)
            self.create_battery_demand_button.setEnabled(False)
            self.clear_battery_details()
            return
        elif len(unique_rows) == 1:
            # Single selection - enable all buttons and show details
            self.edit_battery_button.setEnabled(True)
            self.delete_battery_button.setEnabled(True)
            self.create_battery_demand_button.setEnabled(True)
            
            # Show details for the selected item
            battery_id = self.battery_table.item(unique_rows[0], 0).data(Qt.UserRole)
            
            # Get battery details
            battery = models.Battery.get_by_id(battery_id)
            if not battery:
                self.clear_battery_details()
                return
            
            # Update details display
            equipment_display = f"{battery.get('ba_number', '')} - {battery.get('make_and_type', '')}"
            if not battery.get('ba_number'):
                equipment_display = battery.get('make_and_type', '')
            self.battery_detail_equipment.setText(equipment_display)
            
            self.battery_detail_done_date.setText(battery.get('done_date') or "--")
            
            # Calculate due date and age
            from datetime import datetime, timedelta
            done_date_str = battery.get('done_date')
            custom_life = battery.get('custom_life_months')
            
            if done_date_str and custom_life:
                try:
                    done_date = datetime.strptime(done_date_str, '%Y-%m-%d')
                    due_date = done_date + timedelta(days=custom_life * 30.44)  # Average days per month
                    self.battery_detail_due_date.setText(due_date.strftime('%Y-%m-%d'))
                    
                    # Calculate age in months
                    today = datetime.now()
                    age_months = (today.year - done_date.year) * 12 + (today.month - done_date.month)
                    self.battery_detail_age.setText(f"{age_months} months")
                    
                    # Status
                    if today >= due_date:
                        self.battery_detail_status.setText("Due for Replacement")
                        self.battery_detail_status.setStatus("error")
                    elif (due_date - today).days <= 30:
                        self.battery_detail_status.setText("Replacement Soon")
                        self.battery_detail_status.setStatus("warning")
                    else:
                        self.battery_detail_status.setText("Normal")
                        self.battery_detail_status.setStatus("normal")
                except:
                    self.battery_detail_due_date.setText("--")
                    self.battery_detail_age.setText("--")
                    self.battery_detail_status.setText("--")
                    self.battery_detail_status.setStatus("normal")
            else:
                self.battery_detail_due_date.setText("--")
                self.battery_detail_age.setText("--")
                self.battery_detail_status.setText("--")
                self.battery_detail_status.setStatus("normal")
            
            self.battery_detail_life.setText(f"{custom_life} months" if custom_life else "--")
        else:
            # Multiple selection - enable only bulk operations
            self.edit_battery_button.setEnabled(False)  # Can't edit multiple items
            self.delete_battery_button.setEnabled(True)  # Can delete multiple items
            self.create_battery_demand_button.setEnabled(True)  # Can create demand for multiple items
            
            # Show summary in details
            self.battery_detail_equipment.setText(f"{len(unique_rows)} items selected")
            self.battery_detail_done_date.setText("Multiple")
            self.battery_detail_due_date.setText("Multiple")
            self.battery_detail_life.setText("Multiple")
            self.battery_detail_age.setText("Multiple")
            self.battery_detail_status.setText("Multiple")
            self.battery_detail_status.setStatus("normal")
    
    def clear_battery_details(self):
        """Clear battery detail labels."""
        self.battery_detail_equipment.setText("--")
        self.battery_detail_done_date.setText("--")
        self.battery_detail_due_date.setText("--")
        self.battery_detail_life.setText("--")
        self.battery_detail_age.setText("--")
        self.battery_detail_status.setText("--")
        
        # Disable buttons
        self.edit_battery_button.setEnabled(False)
        self.delete_battery_button.setEnabled(False)
    
    def add_battery(self):
        """Add a new battery record."""
        # Get equipment list for the dialog
        equipment_list = models.Equipment.get_all()
        
        dialog = BatteryDialog(equipment_list=equipment_list, parent=self)
        if dialog.exec_() == BatteryDialog.Accepted:
            battery_data = dialog.get_battery_data()
            
            # Create new battery record
            battery = models.Battery(
                equipment_id=battery_data['equipment_id'],
                done_date=battery_data['done_date'],
                custom_life_months=battery_data['custom_life_months']
            )
            
            battery_id = battery.save()
            if battery_id:
                QMessageBox.information(self, "Success", "Battery record added successfully.")
                self.load_battery_data()
            else:
                QMessageBox.critical(self, "Error", "Failed to add battery record.")
    
    def edit_battery(self):
        """Edit the selected battery record."""
        selected_row = self.battery_table.currentRow()
        if selected_row < 0:
            return
        
        # Get the selected battery ID
        battery_id = self.battery_table.item(selected_row, 0).data(Qt.UserRole)
        
        # Get the battery record
        battery_record = models.Battery.get_by_id(battery_id)
        if not battery_record:
            QMessageBox.warning(self, "Error", "Battery record not found.")
            return
        
        # Get equipment list for the dialog
        equipment_list = models.Equipment.get_all()
        
        dialog = BatteryDialog(battery=battery_record, equipment_list=equipment_list, parent=self)
        if dialog.exec_() == BatteryDialog.Accepted:
            battery_data = dialog.get_battery_data()
            
            # Update battery record
            battery = models.Battery(
                battery_id=battery_data['battery_id'],
                equipment_id=battery_data['equipment_id'],
                done_date=battery_data['done_date'],
                custom_life_months=battery_data['custom_life_months']
            )
            
            if battery.save():
                QMessageBox.information(self, "Success", "Battery record updated successfully.")
                self.load_battery_data()
            else:
                QMessageBox.critical(self, "Error", "Failed to update battery record.")
    
    def delete_battery(self):
        """Delete the selected battery record."""
        selected_row = self.battery_table.currentRow()
        if selected_row < 0:
            return
        
        # Get the selected battery ID
        battery_id = self.battery_table.item(selected_row, 0).data(Qt.UserRole)
        
        # Get equipment name for confirmation
        equipment_item = self.battery_table.item(selected_row, 0)
        equipment_name = equipment_item.text() if equipment_item else "Unknown Equipment"
        
        reply = QMessageBox.question(
            self, "Delete Battery Record", 
            f"Are you sure you want to delete the battery record for {equipment_name}?",
            QMessageBox.Yes | QMessageBox.No, 
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            if models.Battery.delete(battery_id):
                QMessageBox.information(self, "Success", "Battery record deleted successfully.")
                self.load_battery_data()
                self.clear_battery_details()
            else:
                QMessageBox.critical(self, "Error", "Failed to delete battery record.")
    
    def select_all_conditioning(self):
        """Select all conditioning records in the table."""
        if self.conditioning_table.rowCount() == 0:
            QMessageBox.information(self, "No Records", "No conditioning records available to select.")
            return
        
        # Check if all items are already selected
        selected_rows = self.conditioning_table.selectionModel().selectedRows()
        total_rows = self.conditioning_table.rowCount()
        
        if len(selected_rows) == total_rows:
            # All selected - deselect all
            self.conditioning_table.clearSelection()
            self.deselect_all_button.setText("Select All")
            self.deselect_all_button.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; }")
        else:
            # Select all rows
            self.conditioning_table.selectAll()
            self.deselect_all_button.setText("Deselect All")
            self.deselect_all_button.setStyleSheet("QPushButton { background-color: #FF9800; color: white; font-weight: bold; }")
        
        # Trigger selection handling
        if self.conditioning_table.rowCount() > 0:
            self.conditioning_selected(0)

    def select_all_battery(self):
        """Select all battery records in the table."""
        if self.battery_table.rowCount() == 0:
            QMessageBox.information(self, "No Records", "No battery records available to select.")
            return
        
        # Check if all items are already selected
        selected_rows = self.battery_table.selectionModel().selectedRows()
        total_rows = self.battery_table.rowCount()
        
        if len(selected_rows) == total_rows:
            # All selected - deselect all
            self.battery_table.clearSelection()
            self.deselect_all_battery_button.setText("Select All")
            self.deselect_all_battery_button.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; }")
        else:
            # Select all rows
            self.battery_table.selectAll()
            self.deselect_all_battery_button.setText("Deselect All")
            self.deselect_all_battery_button.setStyleSheet("QPushButton { background-color: #FF9800; color: white; font-weight: bold; }")
        
        # Trigger selection handling
        if self.battery_table.rowCount() > 0:
            self.battery_selected(0)

    def toggle_multiselect_conditioning(self):
        """Toggle multi-select mode for conditioning table."""
        if self.multiselect_button.isChecked():
            # Enable multi-select mode
            self.conditioning_table.setSelectionMode(QAbstractItemView.ExtendedSelection)
            self.multiselect_button.setText("Multi-Select ON")
            self.multiselect_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
            QMessageBox.information(
                self, 
                "Multi-Select Mode", 
                "Multi-select mode enabled!\n\nNow you can:\n• Click rows to select them\n• Hold Ctrl+Click for additional selections\n• Click and drag to select multiple rows\n• Use 'Deselect All' to clear selection"
            )
        else:
            # Disable multi-select mode
            self.conditioning_table.setSelectionMode(QAbstractItemView.SingleSelection)
            self.multiselect_button.setText("Multi-Select")
            self.multiselect_button.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; }")
            # Clear any existing selection
            self.conditioning_table.clearSelection()
        
        # Update button states
        self.update_conditioning_button_states()

    def toggle_multiselect_battery(self):
        """Toggle multi-select mode for battery table."""
        if self.multiselect_battery_button.isChecked():
            # Enable multi-select mode
            self.battery_table.setSelectionMode(QAbstractItemView.ExtendedSelection)
            self.multiselect_battery_button.setText("Multi-Select ON")
            self.multiselect_battery_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
            QMessageBox.information(
                self, 
                "Multi-Select Mode", 
                "Multi-select mode enabled!\n\nNow you can:\n• Click rows to select them\n• Hold Ctrl+Click for additional selections\n• Click and drag to select multiple rows\n• Use 'Deselect All' to clear selection"
            )
        else:
            # Disable multi-select mode
            self.battery_table.setSelectionMode(QAbstractItemView.SingleSelection)
            self.multiselect_battery_button.setText("Multi-Select")
            self.multiselect_battery_button.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; }")
            # Clear any existing selection
            self.battery_table.clearSelection()
        
        # Update button states
        self.update_battery_button_states()

    def deselect_all_conditioning(self):
        """Deselect all conditioning records."""
        self.conditioning_table.clearSelection()
        self.update_conditioning_button_states()

    def deselect_all_battery(self):
        """Deselect all battery records."""
        self.battery_table.clearSelection()
        self.update_battery_button_states()

    def update_conditioning_button_states(self):
        """Update button states based on conditioning table selection."""
        selected_rows = [index.row() for index in self.conditioning_table.selectedIndexes()]
        unique_rows = list(set(selected_rows))
        selection_count = len(unique_rows)
        
        # Update button states based on selection
        if selection_count == 0:
            # No selection
            self.edit_button.setEnabled(False)
            self.delete_button.setEnabled(False)
            self.deselect_all_button.setEnabled(False)
            self.create_demand_button.setEnabled(False)
        elif selection_count == 1:
            # Single selection
            self.edit_button.setEnabled(True)
            self.delete_button.setEnabled(True)
            self.deselect_all_button.setEnabled(True)
            self.create_demand_button.setEnabled(True)
        else:
            # Multiple selection
            self.edit_button.setEnabled(False)  # Can't edit multiple records
            self.delete_button.setEnabled(True)
            self.deselect_all_button.setEnabled(True)
            self.create_demand_button.setEnabled(True)

    def update_battery_button_states(self):
        """Update button states based on battery table selection."""
        selected_rows = [index.row() for index in self.battery_table.selectedIndexes()]
        unique_rows = list(set(selected_rows))
        selection_count = len(unique_rows)
        
        # Update button states based on selection
        if selection_count == 0:
            # No selection
            self.edit_battery_button.setEnabled(False)
            self.delete_battery_button.setEnabled(False)
            self.deselect_all_battery_button.setEnabled(False)
            self.create_battery_demand_button.setEnabled(False)
        elif selection_count == 1:
            # Single selection
            self.edit_battery_button.setEnabled(True)
            self.delete_battery_button.setEnabled(True)
            self.deselect_all_battery_button.setEnabled(True)
            self.create_battery_demand_button.setEnabled(True)
        else:
            # Multiple selection
            self.edit_battery_button.setEnabled(False)  # Can't edit multiple records
            self.delete_battery_button.setEnabled(True)
            self.deselect_all_battery_button.setEnabled(True)
            self.create_battery_demand_button.setEnabled(True)

    def create_tyre_demand(self):
        """Create tyre demand forecast from selected conditioning records."""
        selected_rows = [index.row() for index in self.conditioning_table.selectedIndexes()]
        unique_rows = list(set(selected_rows))  # Remove duplicates
        
        if not unique_rows:
            QMessageBox.warning(self, "No Selection", "Please select one or more conditioning records to create demand.")
            return
        
        # Get current fiscal year
        from datetime import datetime
        current_year = datetime.now().year
        fiscal_year = str(current_year)
        
        # Collect selected conditioning records
        conditioning_records = []
        for row in unique_rows:
            conditioning_id = self.conditioning_table.item(row, 0).data(Qt.UserRole)
            conditioning = models.TyreMaintenance.get_by_id(conditioning_id)
            if conditioning:
                conditioning_records.append(conditioning)
        
        if not conditioning_records:
            QMessageBox.warning(self, "Error", "Could not retrieve conditioning records.")
            return
        
        # Show confirmation dialog with details
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QTableWidget, QTableWidgetItem, QPushButton, QComboBox, QSpinBox, QGroupBox, QFormLayout
        
        dialog = QDialog(self)
        dialog.setWindowTitle("Create Tyre Demand Forecast")
        dialog.setModal(True)
        dialog.resize(1200, 750)  # Much larger to accommodate wide columns
        
        layout = QVBoxLayout(dialog)
        
        # Title
        title_label = QLabel("Create Tyre Demand Forecast")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # Fiscal year selection
        fy_group = QGroupBox("Forecast Details")
        fy_layout = QFormLayout(fy_group)
        
        fy_combo = QComboBox()
        for year in range(current_year, current_year + 5):
            fy_combo.addItem(str(year))
        fy_combo.setCurrentText(fiscal_year)
        fy_layout.addRow("Fiscal Year:", fy_combo)
        
        layout.addWidget(fy_group)
        
        # Records table
        records_label = QLabel(f"Selected Records ({len(conditioning_records)} items):")
        layout.addWidget(records_label)
        
        records_table = QTableWidget()
        records_table.setColumnCount(5)
        records_table.setHorizontalHeaderLabels(["Equipment", "Current Quantity", "Tyre Quantity", "Tyre Type", "Remarks"])
        records_table.setRowCount(len(conditioning_records))
        
        # Enable better table formatting
        records_table.setAlternatingRowColors(True)
        records_table.setSelectionBehavior(QTableWidget.SelectRows)
        records_table.verticalHeader().setVisible(False)
        records_table.horizontalHeader().setStretchLastSection(True)
        
        # Set very generous column widths to accommodate widgets and prevent compression
        records_table.setColumnWidth(0, 350)  # Equipment - much wider for full BA numbers and Make & Type
        records_table.setColumnWidth(1, 140)  # Current Quantity - wider for full header
        records_table.setColumnWidth(2, 150)  # Tyre Quantity - extra space for spinbox
        records_table.setColumnWidth(3, 200)  # Tyre Type - much wider for combobox dropdown
        records_table.setColumnWidth(4, 350)  # Remarks - even wider for full text
        
        for row, conditioning in enumerate(conditioning_records):
            # Equipment
            equipment_display = f"{conditioning.get('ba_number', '')} - {conditioning.get('make_and_type', '')}"
            if not conditioning.get('ba_number'):
                equipment_display = conditioning.get('make_and_type', '')
            records_table.setItem(row, 0, QTableWidgetItem(equipment_display))
            
            # Current quantity
            current_qty = conditioning.get('quantity', 1)
            records_table.setItem(row, 1, QTableWidgetItem(str(current_qty)))
            
            # Tyre quantity (editable)
            qty_spinbox = QSpinBox()
            qty_spinbox.setMinimum(1)
            qty_spinbox.setMaximum(50)
            qty_spinbox.setValue(max(1, current_qty))
            qty_spinbox.setMinimumWidth(120)  # Ensure minimum width for proper display
            qty_spinbox.setFixedHeight(25)    # Consistent height
            records_table.setCellWidget(row, 2, qty_spinbox)
            
            # Tyre type (enhanced with editable combobox)
            tyre_type_combo = QComboBox()
            tyre_type_combo.setEditable(True)  # Allow custom input
            
            # Predefined tyre types
            tyre_types = [
                "Cover Outer 1500X21 12 Ply",
                "Cover Outer 1200X20 18 Ply", 
                "Cover Outer 1100X20 16 Ply",
                "Cover Outer 355/90X20 18 Ply",
                "Cover Outer F 78X15 4 Ply",
                "Cover Outer 3.25X19ST 4 Ply",
                "Cover Outer 235/35/70R16",
                "Cover Outer 900x20 14 Ply",
                "Custom"
            ]
            tyre_type_combo.addItems(tyre_types)
            
            # Auto-detect default based on equipment type
            default_tyre_type = "Cover Outer 1500X21 12 Ply"  # Default to first option
            make_type = conditioning.get('make_and_type', '').lower()
            if 'heavy' in make_type or 'truck' in make_type or 'trailer' in make_type:
                default_tyre_type = "Cover Outer 1200X20 18 Ply"
            elif 'light' in make_type or 'car' in make_type or 'jeep' in make_type:
                default_tyre_type = "Cover Outer F 78X15 4 Ply"
            elif 'terrain' in make_type or 'off' in make_type:
                default_tyre_type = "Cover Outer 1100X20 16 Ply"
            
            tyre_type_combo.setCurrentText(default_tyre_type)
            
            # Set minimum size to prevent compression
            tyre_type_combo.setMinimumWidth(160)  # Ensure dropdown displays properly
            tyre_type_combo.setFixedHeight(25)    # Consistent height with spinbox
            
            # Add tooltip
            tyre_type_combo.setToolTip("Select predefined type or enter custom tyre specification")
            
            records_table.setCellWidget(row, 3, tyre_type_combo)
            
            # Remarks
            remarks = f"Auto-generated from conditioning record on {datetime.now().strftime('%Y-%m-%d')}"
            records_table.setItem(row, 4, QTableWidgetItem(remarks))
        
        # Final column adjustments and resize
        records_table.resizeColumnsToContents()
        
        # Ensure minimum widths are maintained for readability and widget display
        min_widths = [250, 140, 150, 200, 300]  # Match the explicit widths set above
        for col in range(records_table.columnCount()):
            if col < len(min_widths) and records_table.columnWidth(col) < min_widths[col]:
                records_table.setColumnWidth(col, min_widths[col])
        
        layout.addWidget(records_table)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(dialog.reject)
        button_layout.addWidget(cancel_button)
        
        create_button = QPushButton("Create Demand")
        create_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        
        def validate_and_create():
            """Validate that all tyre types are selected before creating demand."""
            errors = []
            for row in range(len(conditioning_records)):
                tyre_type_combo = records_table.cellWidget(row, 3)
                tyre_type = tyre_type_combo.currentText().strip()
                if not tyre_type:
                    equipment_name = conditioning_records[row].get('make_and_type', f'Row {row + 1}')
                    errors.append(f"Please select a tyre type for {equipment_name}")
            
            if errors:
                QMessageBox.warning(dialog, "Validation Error", 
                                  "Please fix the following issues:\n\n" + "\n".join(errors))
                return
            
            dialog.accept()
        
        create_button.clicked.connect(validate_and_create)
        button_layout.addWidget(create_button)
        
        layout.addLayout(button_layout)
        
        # Show dialog
        if dialog.exec_() == QDialog.Accepted:
            try:
                selected_fiscal_year = fy_combo.currentText()
                created_count = 0
                errors = []
                
                # Create tyre forecast records
                for row, conditioning in enumerate(conditioning_records):
                    try:
                        # Get quantity from spinbox
                        qty_spinbox = records_table.cellWidget(row, 2)
                        quantity = qty_spinbox.value()
                        
                        # Get tyre type from combobox widget
                        tyre_type_combo = records_table.cellWidget(row, 3)
                        tyre_type = tyre_type_combo.currentText()
                        remarks = records_table.item(row, 4).text()
                        
                        # Create TyreForecast record
                        from models import TyreForecast
                        tyre_forecast = TyreForecast(
                            equipment_id=conditioning.get('equipment_id'),
                            fiscal_year=selected_fiscal_year,
                            tyre_type=tyre_type,
                            quantity_required=quantity,
                            total_requirement=quantity * 1.0,  # Assuming 1 unit cost per tyre
                            remarks=remarks
                        )
                        
                        forecast_id = tyre_forecast.save()
                        if forecast_id:
                            created_count += 1
                        else:
                            errors.append(f"Failed to create demand for {conditioning.get('make_and_type', 'Unknown')}")
                    
                    except Exception as e:
                        errors.append(f"Error creating demand for {conditioning.get('make_and_type', 'Unknown')}: {str(e)}")
                
                # Show results
                if created_count > 0:
                    message = f"Successfully created {created_count} tyre demand forecast(s) for fiscal year {selected_fiscal_year}."
                    if errors:
                        message += f"\n\nErrors:\n" + "\n".join(errors)
                        QMessageBox.warning(self, "Partial Success", message)
                    else:
                        QMessageBox.information(self, "Success", message)
                    
                    # Ask if user wants to switch to demand forecast tab
                    switch_reply = QMessageBox.question(
                        self, 
                        "Switch to Demand Forecast", 
                        "Would you like to switch to the Demand Forecast tab to view the created demands?",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.Yes
                    )
                    
                    if switch_reply == QMessageBox.Yes:
                        # Switch to demand forecast tab
                        main_window = self.window()
                        if hasattr(main_window, 'tab_widget'):
                            # Find demand forecast tab
                            for i in range(main_window.tab_widget.count()):
                                if main_window.tab_widget.tabText(i) == "Demand Forecast":
                                    main_window.tab_widget.setCurrentIndex(i)
                                    # Switch to tyre sub-tab
                                    demand_widget = main_window.tab_widget.widget(i)
                                    if hasattr(demand_widget, 'tab_widget'):
                                        for j in range(demand_widget.tab_widget.count()):
                                            if demand_widget.tab_widget.tabText(j) == "Tyre":
                                                demand_widget.tab_widget.setCurrentIndex(j)
                                                break
                                    break
                else:
                    QMessageBox.critical(self, "Error", "Failed to create any demand forecasts.\n\nErrors:\n" + "\n".join(errors))
            
            except Exception as e:
                QMessageBox.critical(self, "Error", f"An error occurred while creating demand forecasts:\n{str(e)}")
        
        # Refresh the data
        self.load_data()

    def create_battery_demand(self):
        """Create battery demand forecast from selected battery records."""
        selected_rows = [index.row() for index in self.battery_table.selectedIndexes()]
        unique_rows = list(set(selected_rows))  # Remove duplicates
        
        if not unique_rows:
            QMessageBox.warning(self, "No Selection", "Please select one or more battery records to create demand.")
            return
        
        # Get current fiscal year
        from datetime import datetime
        current_year = datetime.now().year
        fiscal_year = str(current_year)
        
        # Collect selected battery records
        battery_records = []
        for row in unique_rows:
            battery_id = self.battery_table.item(row, 0).data(Qt.UserRole)
            battery = models.Battery.get_by_id(battery_id)
            if battery:
                battery_records.append(battery)
        
        if not battery_records:
            QMessageBox.warning(self, "Error", "Could not retrieve battery records.")
            return
        
        # Show confirmation dialog with details
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QTableWidget, QTableWidgetItem, QPushButton, QComboBox, QSpinBox, QGroupBox, QFormLayout, QDoubleSpinBox
        
        dialog = QDialog(self)
        dialog.setWindowTitle("Create Battery Demand Forecast")
        dialog.setModal(True)
        dialog.resize(1600, 900)  # Much larger to accommodate extra wide columns and tall rows
        
        layout = QVBoxLayout(dialog)
        
        # Title
        title_label = QLabel("Create Battery Demand Forecast")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # Fiscal year selection
        fy_group = QGroupBox("Forecast Details")
        fy_layout = QFormLayout(fy_group)
        
        fy_combo = QComboBox()
        for year in range(current_year, current_year + 5):
            fy_combo.addItem(str(year))
        fy_combo.setCurrentText(fiscal_year)
        fy_layout.addRow("Fiscal Year:", fy_combo)
        
        layout.addWidget(fy_group)
        
        # Records table
        records_label = QLabel(f"Selected Records ({len(battery_records)} items):")
        layout.addWidget(records_label)
        
        records_table = QTableWidget()
        records_table.setColumnCount(6)
        records_table.setHorizontalHeaderLabels(["Equipment", "Quantity", "Voltage", "AH", "Battery Type", "Remarks"])
        records_table.setRowCount(len(battery_records))
        
        for row, battery in enumerate(battery_records):
            # Equipment
            equipment_display = f"{battery.get('ba_number', '')} - {battery.get('make_and_type', '')}"
            if not battery.get('ba_number'):
                equipment_display = battery.get('make_and_type', '')
            records_table.setItem(row, 0, QTableWidgetItem(equipment_display))
            
            # Quantity (editable)
            qty_spinbox = QSpinBox()
            qty_spinbox.setMinimum(1)
            qty_spinbox.setMaximum(20)
            qty_spinbox.setValue(1)  # Default 1 battery per equipment
            qty_spinbox.setMinimumWidth(60)  # Ensure minimum width
            records_table.setCellWidget(row, 1, qty_spinbox)
            
            # Voltage dropdown with 12V, 24V and Custom option
            voltage_combo = QComboBox()
            voltage_combo.addItem("12V", 12)
            voltage_combo.addItem("24V", 24)
            voltage_combo.addItem("Custom", 0)
            voltage_combo.setEditable(True)  # Make it always editable
            voltage_combo.setInsertPolicy(QComboBox.NoInsert)  # Prevent adding new items
            
            # Auto-detect voltage based on equipment type
            make_type = battery.get('make_and_type', '').lower()
            if 'heavy' in make_type or 'truck' in make_type or 'bus' in make_type:
                voltage_combo.setCurrentText("24V")
            else:
                voltage_combo.setCurrentText("12V")
            
            # When Custom is selected, clear the field for custom input
            def on_voltage_changed(combo_widget):
                if combo_widget.currentText() == "Custom":
                    # Clear the text and set focus for immediate typing
                    combo_widget.clearEditText()
                    line_edit = combo_widget.lineEdit()
                    if line_edit:
                        line_edit.setPlaceholderText("Enter voltage (e.g., 18, 36, 48)")
                        line_edit.setFocus()  # Set focus so user can immediately type
                        line_edit.selectAll()  # Select all text if any exists
            
            voltage_combo.currentTextChanged.connect(lambda text, combo=voltage_combo: on_voltage_changed(combo))
            voltage_combo.setMinimumWidth(80)  # Ensure minimum width for dropdown
            records_table.setCellWidget(row, 2, voltage_combo)
            
            # AH (Ampere Hours) field - number only
            ah_spinbox = QSpinBox()
            ah_spinbox.setMinimum(1)
            ah_spinbox.setMaximum(9999)
            ah_spinbox.setValue(125)  # Default 125 AH
            ah_spinbox.setSuffix(" AH")
            ah_spinbox.setMinimumWidth(70)  # Ensure minimum width for spinbox with suffix
            records_table.setCellWidget(row, 3, ah_spinbox)
            
            # Battery type will be auto-generated based on voltage and AH
            def update_battery_type():
                voltage_widget = records_table.cellWidget(row, 2)
                ah_widget = records_table.cellWidget(row, 3)
                if voltage_widget and ah_widget:
                    # Get the actual text from the editable combo box
                    current_text = voltage_widget.currentText().strip()
                    
                    # If it's one of the preset values, use it directly
                    if current_text in ["12V", "24V"]:
                        voltage_value = current_text
                    elif current_text == "Custom":
                        # If "Custom" is still showing, use default
                        voltage_value = "12V"
                    else:
                        # It's a custom value that was typed
                        if current_text:
                            # Add 'V' if not present
                            if not current_text.upper().endswith('V'):
                                voltage_value = f"{current_text}V"
                            else:
                                voltage_value = current_text
                        else:
                            voltage_value = "12V"  # Default fallback
                    
                    ah_value = ah_widget.value()
                    battery_type = f"{voltage_value}{ah_value}AH"
                    battery_type_item = records_table.item(row, 4)
                    if battery_type_item:
                        battery_type_item.setText(battery_type)
            
            # Connect signals to update battery type automatically
            voltage_combo.currentTextChanged.connect(update_battery_type)
            ah_spinbox.valueChanged.connect(update_battery_type)
            
            # Also connect the line edit text change for custom voltage input
            def connect_line_edit_signal():
                line_edit = voltage_combo.lineEdit()
                if line_edit:
                    line_edit.textChanged.connect(update_battery_type)
            
            # Connect line edit signal when combo becomes editable
            voltage_combo.editTextChanged.connect(update_battery_type)
            QTimer.singleShot(100, connect_line_edit_signal)  # Ensure line edit is available
            
            # Set initial battery type
            initial_voltage = voltage_combo.currentText()
            initial_ah = ah_spinbox.value()
            initial_battery_type = f"{initial_voltage}{initial_ah}AH"
            records_table.setItem(row, 4, QTableWidgetItem(initial_battery_type))
            
            # Remarks
            remarks = f"Auto-generated from battery record on {datetime.now().strftime('%Y-%m-%d')}"
            records_table.setItem(row, 5, QTableWidgetItem(remarks))
        
        # Set column widths manually to ensure widgets are visible - EXTRA WIDE
        records_table.setColumnWidth(0, 400)  # Equipment - much wider for full BA numbers and Make & Type
        records_table.setColumnWidth(1, 120)  # Quantity - much wider for spinbox
        records_table.setColumnWidth(2, 150)  # Voltage - much wider for dropdown with custom input
        records_table.setColumnWidth(3, 130)  # AH - much wider for spinbox with suffix
        records_table.setColumnWidth(4, 160)  # Battery Type - much wider for type string
        records_table.setColumnWidth(5, 450)  # Remarks - much wider for full text
        
        # Set row height to ensure widgets are properly visible - EXTRA TALL
        for row in range(records_table.rowCount()):
            records_table.setRowHeight(row, 50)  # Much taller rows for better widget visibility
        
        # Enable horizontal scrolling if needed
        records_table.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # Enable vertical scrolling if needed
        records_table.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        layout.addWidget(records_table)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(dialog.reject)
        button_layout.addWidget(cancel_button)
        
        create_button = QPushButton("Create Demand")
        create_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        create_button.clicked.connect(dialog.accept)
        button_layout.addWidget(create_button)
        
        layout.addLayout(button_layout)
        
        # Show dialog
        if dialog.exec_() == QDialog.Accepted:
            try:
                selected_fiscal_year = fy_combo.currentText()
                created_count = 0
                errors = []
                
                # Create battery forecast records
                for row, battery in enumerate(battery_records):
                    try:
                        # Get values from widgets
                        qty_spinbox = records_table.cellWidget(row, 1)
                        quantity = qty_spinbox.value()
                        
                        voltage_combo = records_table.cellWidget(row, 2)
                        voltage_text = voltage_combo.currentText()
                        if voltage_text == "Custom":
                            if voltage_combo.lineEdit() and voltage_combo.lineEdit().text():
                                voltage_value = voltage_combo.lineEdit().text().replace('V', '')
                                try:
                                    voltage = float(voltage_value)
                                except ValueError:
                                    voltage = 12.0  # Default fallback
                            else:
                                voltage = 12.0
                        else:
                            voltage = float(voltage_text.replace('V', ''))
                        
                        ah_spinbox = records_table.cellWidget(row, 3)
                        ampere_hours = ah_spinbox.value()
                        
                        battery_type = records_table.item(row, 4).text()
                        
                        remarks = records_table.item(row, 5).text()
                        
                        # Calculate total requirement
                        total_requirement = quantity * 1.0
                        
                        # Create BatteryForecast record
                        from models import BatteryForecast
                        battery_forecast = BatteryForecast(
                            equipment_id=battery.get('equipment_id'),
                            fiscal_year=selected_fiscal_year,
                            battery_type=battery_type,
                            voltage=voltage,
                            ampere_hours=ampere_hours,
                            quantity_required=quantity,
                            total_requirement=total_requirement,
                            remarks=remarks
                        )
                        
                        forecast_id = battery_forecast.save()
                        if forecast_id:
                            created_count += 1
                        else:
                            errors.append(f"Failed to create demand for {battery.get('make_and_type', 'Unknown')}")
                    
                    except Exception as e:
                        errors.append(f"Error creating demand for {battery.get('make_and_type', 'Unknown')}: {str(e)}")
                
                # Show results
                if created_count > 0:
                    message = f"Successfully created {created_count} battery demand forecast(s) for fiscal year {selected_fiscal_year}."
                    if errors:
                        message += f"\n\nErrors:\n" + "\n".join(errors)
                        QMessageBox.warning(self, "Partial Success", message)
                    else:
                        QMessageBox.information(self, "Success", message)
                    
                    # Ask if user wants to switch to demand forecast tab
                    switch_reply = QMessageBox.question(
                        self, 
                        "Switch to Demand Forecast", 
                        "Would you like to switch to the Demand Forecast tab to view the created demands?",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.Yes
                    )
                    
                    if switch_reply == QMessageBox.Yes:
                        # Switch to demand forecast tab
                        main_window = self.window()
                        if hasattr(main_window, 'tab_widget'):
                            # Find demand forecast tab
                            for i in range(main_window.tab_widget.count()):
                                if main_window.tab_widget.tabText(i) == "Demand Forecast":
                                    main_window.tab_widget.setCurrentIndex(i)
                                    # Switch to battery sub-tab
                                    demand_widget = main_window.tab_widget.widget(i)
                                    if hasattr(demand_widget, 'tab_widget'):
                                        for j in range(demand_widget.tab_widget.count()):
                                            if demand_widget.tab_widget.tabText(j) == "Battery":
                                                demand_widget.tab_widget.setCurrentIndex(j)
                                                break
                                    break
                else:
                    QMessageBox.critical(self, "Error", "Failed to create any demand forecasts.\n\nErrors:\n" + "\n".join(errors))
            
            except Exception as e:
                QMessageBox.critical(self, "Error", f"An error occurred while creating demand forecasts:\n{str(e)}")
        
        # Refresh the data
        self.load_data()

