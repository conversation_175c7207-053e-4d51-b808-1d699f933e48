"""
Working Excel Importer - Actually saves data to production database
"""

import pandas as pd
import numpy as np
import os
import logging
import re
import xlrd
import sqlite3
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime, date

logger = logging.getLogger('working_excel_importer')

class RobustExcelImporter:
    """Working Excel importer that saves to production database."""
    
    def __init__(self, staging_db_path: str = 'staging.db'):
        # Import config here to get the correct DB path
        import config
        # Always use the production database path for actual operations
        self.db_path = config.DB_PATH
        self.session_id = f"import_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        logger.info(f"Initialized WorkingExcelImporter with session {self.session_id}, using database {self.db_path}")
    
    def initialize_staging(self) -> bool:
        """Initialize the production database."""
        try:
            # Initialize production database instead of staging
            import database
            database.init_db()
            
            # Ensure equipment table exists
            import sqlite3
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create equipment table if it doesn't exist
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS equipment (
                    equipment_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    serial_number TEXT,
                    make_and_type TEXT,
                    ba_number TEXT,
                    units_held INTEGER DEFAULT 1,
                    vintage_years REAL DEFAULT 0,
                    meterage_kms REAL DEFAULT 0,
                    km_hrs_run_previous_month REAL DEFAULT 0,
                    km_hrs_run_current_month REAL DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    remarks TEXT,
                    date_of_commission TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info("Production database and tables initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize production database: {e}")
            return False
    
    def _migrate_schemas(self):
        """Migrate all database schemas to the latest version."""
        # Use consistent database path
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        logger.info(f"Migrating schema for database {self.db_path}")
        
        try:
            # Migrate fluids table if needed
            self._migrate_fluids_schema(cursor)
            
            # Migrate tyres table if needed
            self._migrate_tyres_schema(cursor)
            
            # Migrate discard_criteria table if needed
            self._migrate_discard_criteria_schema(cursor)
            
            conn.commit()
            logger.info("Database schemas migrated successfully")
        except Exception as e:
            logger.error(f"Error during schema migration: {e}")
            conn.rollback()
        finally:
            conn.close()
    
    def _migrate_fluids_schema(self, cursor):
        """Add new columns to fluids table if needed."""
        try:
            # Check if the fluids table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='fluids'")
            if cursor.fetchone():
                # Check for assembly_name column
                cursor.execute("PRAGMA table_info(fluids)")
                columns = [col[1] for col in cursor.fetchall()]
                
                # Add missing columns if they don't exist
                if 'assembly_name' not in columns:
                    logger.info("Adding assembly_name column to fluids table")
                    cursor.execute("ALTER TABLE fluids ADD COLUMN assembly_name TEXT")
                
                if 'top_up' not in columns:
                    logger.info("Adding top_up column to fluids table")
                    cursor.execute("ALTER TABLE fluids ADD COLUMN top_up REAL")
            else:
                logger.info("Fluids table doesn't exist yet, will be created when needed")
        except Exception as e:
            logger.error(f"Error migrating fluids schema: {e}")
            raise
    
    def _migrate_tyres_schema(self, cursor):
        """Add new columns to tyres table if needed."""
        try:
            # Check if the tyres table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='tyres'")
            if cursor.fetchone():
                # Check for condition and life columns
                cursor.execute("PRAGMA table_info(tyres)")
                columns = [col[1] for col in cursor.fetchall()]
                
                # Add missing columns
                if 'condition' not in columns:
                    logger.info("Adding condition column to tyres table")
                    cursor.execute("ALTER TABLE tyres ADD COLUMN condition TEXT")
                
                if 'life' not in columns:
                    logger.info("Adding life column to tyres table")
                    cursor.execute("ALTER TABLE tyres ADD COLUMN life TEXT")
            else:
                logger.info("Tyres table doesn't exist yet, will be created when needed")
        except Exception as e:
            logger.error(f"Error migrating tyres schema: {e}")
            raise
    
    def _migrate_discard_criteria_schema(self, cursor):
        """Add new columns to discard_criteria table if needed."""
        try:
            # Check if the discard_criteria table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='discard_criteria'")
            if cursor.fetchone():
                # Check for hours, service_life and total_life columns
                cursor.execute("PRAGMA table_info(discard_criteria)")
                columns = [col[1] for col in cursor.fetchall()]
                
                # Add missing columns
                if 'hours' not in columns:
                    logger.info("Adding hours column to discard_criteria table")
                    cursor.execute("ALTER TABLE discard_criteria ADD COLUMN hours REAL")
                
                if 'service_life' not in columns:
                    logger.info("Adding service_life column to discard_criteria table")
                    cursor.execute("ALTER TABLE discard_criteria ADD COLUMN service_life TEXT")
                
                if 'total_life' not in columns:
                    logger.info("Adding total_life column to discard_criteria table")
                    cursor.execute("ALTER TABLE discard_criteria ADD COLUMN total_life TEXT")
            else:
                logger.info("Discard criteria table doesn't exist yet, will be created when needed")
        except Exception as e:
            logger.error(f"Error migrating discard_criteria schema: {e}")
            raise
    
    def process_excel_file(self, file_path: str) -> Tuple[bool, Dict[str, Any]]:
        """Process Excel file and save to production database with enhanced progress monitoring."""
        # Initialize progress monitoring
        from import_progress_monitor import start_import_monitoring
        monitor = start_import_monitoring(file_path, self.session_id)

        try:
            monitor.start_phase("SCHEMA_MIGRATION")
            # Migrate schemas first to ensure all tables have the latest columns
            self._migrate_schemas()
            monitor.log_operation("Database schema migration completed")
            monitor.end_phase("COMPLETED")

            monitor.start_phase("FILE_READING")
            logger.info(f"Processing Excel file with data extraction: {file_path}")

            # Read Excel file
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names
            monitor.log_operation("Excel file opened", {"sheets": sheet_names, "sheet_count": len(sheet_names)})
            monitor.end_phase("COMPLETED")

            stats = {
                'total_equipment': 0,
                'total_fluids': 0,
                'total_maintenance': 0,
                'total_overhauls': 0,
                'total_conditioning': 0,
                'total_batteries': 0,
                'total_repairs': 0,
                'total_discard_criteria': 0,
                'total_conditioning': 0,
                'total_skipped': 0,
                'session_id': self.session_id,
                'sheets_processed': [],
                'progress_session_id': monitor.session_id
            }
            
            monitor.start_phase("DATA_PROCESSING")

            # Process each sheet and extract real data
            for sheet_name in sheet_names:
                logger.info(f"Processing sheet: {sheet_name}")

                try:
                    # Read sheet with header detection
                    df = self._read_sheet_with_headers(excel_file, sheet_name)

                    if df is None:
                        monitor.log_warning(f"Could not read sheet: {sheet_name}", {"reason": "header_detection_failed"})
                        stats['total_skipped'] += 1
                        continue

                    monitor.start_sheet_processing(sheet_name, len(df))
                    monitor.log_operation(f"Sheet read successfully", {"rows": len(df), "columns": len(df.columns)})

                    # Extract and save equipment data
                    monitor.log_operation("Processing equipment data")
                    equipment_count = self._extract_and_save_equipment(df, sheet_name)
                    stats['total_equipment'] += equipment_count
                    monitor.update_sheet_progress(sheet_name, len(df) // 6, {"equipment_processed": equipment_count})

                    # Extract and save fluids data for the equipment in this sheet
                    monitor.log_operation("Processing fluids data")
                    fluids_count = self._extract_and_save_fluids(df, sheet_name)
                    stats['total_fluids'] += fluids_count
                    monitor.update_sheet_progress(sheet_name, (len(df) * 2) // 6, {"fluids_processed": fluids_count})

                    # Extract and save maintenance data for the equipment in this sheet
                    monitor.log_operation("Processing maintenance data")
                    maintenance_count = self._extract_and_save_maintenance(df, sheet_name)
                    stats['total_maintenance'] += maintenance_count
                    monitor.update_sheet_progress(sheet_name, (len(df) * 3) // 6, {"maintenance_processed": maintenance_count})

                    # Extract and save overhaul data for the equipment in this sheet
                    monitor.log_operation("Processing overhaul data")
                    overhaul_count = self._extract_and_save_overhauls(df, sheet_name)
                    stats['total_overhauls'] += overhaul_count
                    monitor.update_sheet_progress(sheet_name, (len(df) * 4) // 6, {"overhauls_processed": overhaul_count})

                    # Extract and save conditioning data for the equipment in this sheet
                    monitor.log_operation("Processing conditioning data")
                    conditioning_count = self._extract_and_save_conditioning(df, sheet_name)
                    stats['total_conditioning'] += conditioning_count
                    monitor.update_sheet_progress(sheet_name, (len(df) * 5) // 6, {"conditioning_processed": conditioning_count})

                    # Extract and save battery data for the equipment in this sheet
                    monitor.log_operation("Processing battery data")
                    battery_count = self._extract_and_save_batteries(df, sheet_name)
                    stats['total_batteries'] += battery_count
                    monitor.update_sheet_progress(sheet_name, len(df), {"batteries_processed": battery_count})

                    stats['sheets_processed'].append(sheet_name)

                    sheet_totals = {
                        'equipment': equipment_count,
                        'fluids': fluids_count,
                        'maintenance': maintenance_count,
                        'overhauls': overhaul_count,
                        'conditioning': conditioning_count,
                        'batteries': battery_count
                    }
                    monitor.end_sheet_processing(sheet_name, "COMPLETED", sheet_totals)

                except Exception as e:
                    error_msg = f"Error processing sheet {sheet_name}: {e}"
                    logger.error(error_msg)
                    monitor.log_error(error_msg, {"sheet": sheet_name})
                    monitor.end_sheet_processing(sheet_name, "FAILED")
                    stats['total_skipped'] += 1
            
            monitor.end_phase("COMPLETED")

            logger.info(f"Excel processing completed: {stats}")

            # Trigger post-import overhaul status recalculation
            monitor.start_phase("POST_PROCESSING")
            try:
                monitor.log_operation("Starting overhaul status recalculation")
                logger.info("Starting post-import overhaul status recalculation...")
                import overhaul_service
                updated_count = overhaul_service.update_overhaul_statuses()
                logger.info(f"Post-import status recalculation completed: {updated_count} overhaul statuses updated")
                stats['overhaul_statuses_updated'] = updated_count
                monitor.log_operation("Overhaul status recalculation completed", {"updated_count": updated_count})
            except Exception as e:
                error_msg = f"Error during post-import overhaul status recalculation: {e}"
                logger.error(error_msg)
                monitor.log_error(error_msg)
                stats['overhaul_status_update_error'] = str(e)
            monitor.end_phase("COMPLETED")

            monitor.end_import("COMPLETED", stats)
            return True, stats

        except Exception as e:
            error_msg = f"Error processing Excel file: {e}"
            logger.error(error_msg)
            monitor.log_error(error_msg)
            monitor.end_import("FAILED", {'error': str(e)})
            return False, {'error': str(e), 'progress_session_id': monitor.session_id}
    
    def _read_sheet_with_headers(self, excel_file: pd.ExcelFile, sheet_name: str) -> Optional[pd.DataFrame]:
        """Read sheet with enhanced header detection for malformed and unnamed columns."""
        logger.info(f"Reading sheet with enhanced header detection: {sheet_name}")

        # Try first with explicit multi-level headers (0, 1)
        try:
            # Read with header rows 0 and 1 to capture the multi-level structure
            df = pd.read_excel(excel_file, sheet_name=sheet_name, header=[0, 1])

            # Enhanced multi-level column flattening with unnamed column handling
            if isinstance(df.columns, pd.MultiIndex):
                new_columns = []
                unnamed_counter = 0

                for i, col in enumerate(df.columns):
                    # Clean up column parts
                    parts = []
                    for c in col:
                        c_str = str(c).strip()
                        # Skip nan, empty, or unnamed parts
                        if c_str and c_str.lower() not in ['nan', 'none', 'null'] and not c_str.startswith('Unnamed'):
                            parts.append(c_str)

                    if len(parts) >= 2:
                        # For fluid columns, format as "FLUID_TYPE -> ATTRIBUTE"
                        fluid_type = parts[0]
                        attribute = parts[1]

                        # Special handling for primary columns that shouldn't have fluid types
                        primary_cols = ['SER NO', 'BA NO', 'MAKE & TYPE', 'KM RUN', 'HRS RUN', 'DATE OF REL', 'REMARKS', 'VINTAGE']
                        if any(primary.lower() in fluid_type.lower() for primary in primary_cols):
                            new_columns.append(fluid_type)
                        else:
                            new_columns.append(f"{fluid_type} -> {attribute}")
                    elif len(parts) == 1:
                        # Single meaningful part
                        new_columns.append(parts[0])
                    else:
                        # No meaningful parts - create a placeholder
                        unnamed_counter += 1
                        new_columns.append(f"SKIP_COLUMN_{unnamed_counter}")
                        logger.debug(f"Created placeholder for unnamed column at index {i}")

                df.columns = new_columns

                # Filter out placeholder columns
                columns_to_keep = [col for col in df.columns if not col.startswith('SKIP_COLUMN_')]
                if len(columns_to_keep) < len(df.columns):
                    logger.info(f"Filtering out {len(df.columns) - len(columns_to_keep)} unnamed/empty columns")
                    df = df[columns_to_keep]

            # Check if this looks like a valid data frame with our expected primary columns
            expected_cols = ['SER NO', 'BA NO', 'MAKE & TYPE', 'KM RUN', 'HRS RUN', 'DATE OF REL']
            col_matches = [any(exp.lower() in str(col).lower() for exp in expected_cols) for col in df.columns]

            if sum(col_matches) >= 2 and len(df) > 0:  # At least 2 expected columns found (relaxed requirement)
                logger.info(f"Successfully read sheet with multi-level headers: {sheet_name}")
                logger.info(f"Valid columns found: {len(df.columns)}, Data rows: {len(df)}")
                logger.debug(f"Column sample: {df.columns.tolist()[:10]}")
                return df

        except Exception as e:
            logger.warning(f"Error reading multi-level headers: {e}")

        # If multi-level approach fails, try other header configurations with enhanced cleanup
        for header_config in [[0], [0, 1, 2], None]:
            try:
                df = pd.read_excel(excel_file, sheet_name=sheet_name, header=header_config)

                # Enhanced column cleanup for single-level headers
                if header_config is not None:
                    cleaned_columns = []
                    unnamed_counter = 0

                    for i, col in enumerate(df.columns):
                        col_str = str(col).strip()

                        # Handle unnamed columns
                        if 'Unnamed:' in col_str or col_str.startswith('Unnamed') or col_str in ['nan', 'NaN', '', 'None']:
                            unnamed_counter += 1
                            cleaned_columns.append(f"SKIP_COLUMN_{unnamed_counter}")
                            logger.debug(f"Found unnamed/invalid column at index {i}: '{col_str}'")
                        else:
                            cleaned_columns.append(col_str)

                    df.columns = cleaned_columns

                    # Filter out placeholder columns
                    columns_to_keep = [col for col in df.columns if not col.startswith('SKIP_COLUMN_')]
                    if len(columns_to_keep) < len(df.columns):
                        logger.info(f"Filtering out {len(df.columns) - len(columns_to_keep)} unnamed/empty columns")
                        df = df[columns_to_keep]

                # Check if this looks like a valid data frame
                if len(df.columns) > 2 and len(df) > 0:  # Relaxed requirement
                    logger.info(f"Read sheet with header config {header_config}: {sheet_name}")
                    logger.info(f"Final columns: {len(df.columns)}, Data rows: {len(df)}")
                    return df

            except Exception as e:
                logger.warning(f"Error with header config {header_config}: {e}")

        logger.error(f"Failed to read sheet with any header configuration: {sheet_name}")
        return None
    
    def _extract_and_save_equipment(self, df: pd.DataFrame, sheet_name: str) -> int:
        """Extract equipment data from DataFrame and save to database with BA Number update support."""
        from models import Equipment

        equipment_count = 0
        equipment_updated = 0

        # Find equipment-related columns
        col_map = self._map_equipment_columns(df.columns)

        logger.info(f"Column mapping for {sheet_name}: {col_map}")

        for index, row in df.iterrows():
            try:
                # Extract equipment data
                equipment_data = self._extract_equipment_data(row, col_map, sheet_name)

                # Filter out invalid records (validation rows, headers, etc.)
                if equipment_data and equipment_data.get('make_and_type') and self._is_valid_equipment_record(equipment_data):
                    # Check if equipment with this BA Number already exists
                    existing_equipment_id = self._find_equipment_by_ba_number(equipment_data.get('ba_number'))

                    if existing_equipment_id:
                        # Update existing equipment
                        if self._update_existing_equipment(existing_equipment_id, equipment_data, sheet_name):
                            equipment_updated += 1
                            logger.info(f"Updated equipment with BA Number {equipment_data.get('ba_number')}: {equipment_data['make_and_type']}")
                        continue

                    # Create new equipment if no existing BA Number found
                    import sqlite3
                    import config
                    conn = sqlite3.connect(config.DB_PATH)
                    cursor = conn.cursor()

                    try:
                        # Create the equipment record first
                        cursor.execute('''
                            INSERT INTO equipment (
                                serial_number, make_and_type, ba_number, units_held,
                                vintage_years, meterage_kms, hours_run_total,
                                km_hrs_run_previous_month, km_hrs_run_current_month, 
                                hours_run_previous_month, hours_run_current_month,
                                is_active, remarks, date_of_commission
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            equipment_data.get('serial_number', f"AUTO_{equipment_count + 1}"),
                            equipment_data['make_and_type'],
                            equipment_data.get('ba_number'),
                            equipment_data.get('units_held', 1),
                            equipment_data.get('vintage_years', 0),
                            equipment_data.get('meterage_kms', 0),
                            equipment_data.get('hours_run_total', 0),
                            equipment_data.get('km_hrs_run_previous_month', 0),
                            equipment_data.get('km_hrs_run_current_month', 0),
                            equipment_data.get('hours_run_previous_month', 0),
                            equipment_data.get('hours_run_current_month', 0),
                            1,  # is_active
                            equipment_data.get('remarks', f"Imported from {sheet_name}"),
                            equipment_data.get('date_of_commission')
                        ))
                        
                        # Get the equipment ID for the newly inserted record
                        equipment_id = cursor.lastrowid
                        logger.info(f"Created equipment ID: {equipment_id}")
                        
                        # Check for fluid data and insert if present
                        fluid_types = [
                            ('engine_oil', 'ENG OIL'),
                            ('transmission_oil', 'Transmission Oil'),
                            ('differential_oil', 'Differential Oil'),
                            ('hydraulic_oil', 'Hydraulic Oil'),
                            ('hydraulic_fluid', 'Hydraulic Fluid'),
                            ('coolant', 'Coolant'),
                            ('brake_clutch', 'Brake & Clutch Fluid'),
                            ('clutch_oil', 'Clutch Oil'),
                            ('steering_oil', 'Steering Oil'),
                            ('hub_oil', 'Hub Oil'),
                            ('front_axle', 'Front Axle'),
                            ('rear_axle', 'Rear Axle'),
                            ('gear_box', 'Gear Box'),
                            ('grease', 'Grease'),
                            ('hub_grease', 'Hub Grease'),
                            ('nipple_grease', 'Nipple Grease'),
                            ('engine_clutch_grease', 'Engine Clutch Grease'),
                            ('wheel_arms_grease', 'Wheel Arms Grease'),
                            ('track_mech_grease', 'Track Mechanism Grease')
                        ]
                        
                        # Insert fluid records
                        for fluid_key, fluid_name in fluid_types:
                            capacity_key = f"{fluid_key}_capacity"
                            grade_key = f"{fluid_key}_grade"
                            last_change_key = f"{fluid_key}_last_change_date"
                            periodicity_key = f"{fluid_key}_periodicity"
                            
                            # Check if we have any fluid data for this type
                            if any(key in equipment_data for key in [capacity_key, grade_key, last_change_key, periodicity_key]):
                                try:
                                    # First check if fluid table exists
                                    cursor.execute('''
                                        CREATE TABLE IF NOT EXISTS fluids (
                                            fluid_id INTEGER PRIMARY KEY AUTOINCREMENT,
                                            equipment_id INTEGER,
                                            fluid_type TEXT,
                                            assembly_name TEXT,
                                            capacity REAL,
                                            grade TEXT,
                                            last_change_date TEXT,
                                            periodicity TEXT,
                                            top_up REAL,
                                            FOREIGN KEY (equipment_id) REFERENCES equipment(equipment_id)
                                        )
                                    ''')
                                    
                                    # Add the top-up key
                                    top_up_key = f"{fluid_key}_top_up"
                                    
                                    # Create a consistent assembly name for UI display
                                    # This helps when the UI shows "Assembly" instead of "Fluid Type"
                                    assembly_name = fluid_name
                                    if fluid_name.endswith(" Oil") or fluid_name.endswith(" Fluid") or fluid_name.endswith(" Grease"):
                                        assembly_name = fluid_name
                                    elif "axle" in fluid_key.lower():
                                        assembly_name = f"{fluid_name} Assembly"
                                    elif "gear" in fluid_key.lower():
                                        assembly_name = "Gear Box"
                                    
                                    # Log the fluid data values for debugging
                                    capacity_val = equipment_data.get(capacity_key, 0)
                                    grade_val = equipment_data.get(grade_key, '')
                                    last_change_val = equipment_data.get(last_change_key, '')
                                    periodicity_val = equipment_data.get(periodicity_key, '')
                                    top_up_val = equipment_data.get(top_up_key, 0)
                                    
                                    logger.info(f"Fluid data for {fluid_name}: capacity={capacity_val}, grade={grade_val}, "  
                                               f"last_change={last_change_val}, periodicity={periodicity_val}, top_up={top_up_val}")
                                    
                                    # Insert the fluid record with additional fields
                                    cursor.execute('''
                                        INSERT INTO fluids (
                                            equipment_id, fluid_type, assembly_name, capacity, grade, 
                                            last_change_date, periodicity, top_up
                                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                                    ''', (
                                        equipment_id,
                                        fluid_name,
                                        assembly_name,
                                        capacity_val,
                                        grade_val,
                                        last_change_val,
                                        periodicity_val,
                                        top_up_val
                                    ))
                                    logger.info(f"Added {fluid_name} data for equipment {equipment_id}")
                                except Exception as e:
                                    logger.error(f"Error saving fluid data: {e}")
                        
                        # REMOVED: Legacy maintenance/overhaul record creation
                        # This was creating duplicate maintenance records using hardcoded field names
                        # Maintenance records are now properly handled by _extract_and_save_maintenance() method
                        
                        # Check for Break Out Holding (BOH) data and insert if present
                        if 'break_out_holding' in equipment_data:
                            try:
                                # First check if boh table exists
                                cursor.execute('''
                                    CREATE TABLE IF NOT EXISTS break_out_holding (
                                        boh_id INTEGER PRIMARY KEY AUTOINCREMENT,
                                        equipment_id INTEGER,
                                        done_date TEXT,
                                        due_date TEXT,
                                        FOREIGN KEY (equipment_id) REFERENCES equipment(equipment_id)
                                    )
                                ''')
                                
                                # Insert the BOH record
                                cursor.execute('''
                                    INSERT INTO break_out_holding (
                                        equipment_id, done_date, due_date
                                    ) VALUES (?, ?, ?)
                                ''', (
                                    equipment_id,
                                    equipment_data.get('break_out_holding_done', ''),
                                    equipment_data.get('break_out_holding_due', '')
                                ))
                                logger.info(f"Added BOH data for equipment {equipment_id}")
                            except Exception as e:
                                logger.error(f"Error saving BOH data: {e}")
                        
                        # Check for tyre data and insert if present
                        if any(key in equipment_data for key in ['tyre_rotation_kms', 'tyre_last_change_date', 'tyre_quantity', 'tyre_condition', 'tyre_life']):
                            try:
                                # Create tyres table if it doesn't exist
                                cursor.execute('''
                                    CREATE TABLE IF NOT EXISTS tyres (
                                        tyre_id INTEGER PRIMARY KEY AUTOINCREMENT,
                                        equipment_id INTEGER,
                                        rotation_kms REAL,
                                        last_change_date TEXT,
                                        quantity INTEGER,
                                        condition TEXT,
                                        life TEXT,
                                        FOREIGN KEY (equipment_id) REFERENCES equipment(equipment_id)
                                    )
                                ''')
                                
                                # Insert the tyre record
                                cursor.execute('''
                                    INSERT INTO tyres (
                                        equipment_id, rotation_kms, last_change_date, quantity, condition, life
                                    ) VALUES (?, ?, ?, ?, ?, ?)
                                ''', (
                                    equipment_id,
                                    equipment_data.get('tyre_rotation_kms', 0),
                                    equipment_data.get('tyre_last_change_date', ''),
                                    equipment_data.get('tyre_quantity', 0),
                                    equipment_data.get('tyre_condition', ''),
                                    equipment_data.get('tyre_life', 0)
                                ))
                                logger.info(f"Added tyre data for equipment {equipment_id}")
                            except Exception as e:
                                logger.error(f"Error saving tyre data: {e}")
                        
                        # Check for battery data and insert if present
                        if 'battery_last_change_date' in equipment_data or 'battery_life' in equipment_data:
                            try:
                                # Create batteries table if it doesn't exist
                                cursor.execute('''
                                    CREATE TABLE IF NOT EXISTS batteries (
                                        battery_id INTEGER PRIMARY KEY AUTOINCREMENT,
                                        equipment_id INTEGER,
                                        last_change_date TEXT,
                                        life_months INTEGER,
                                        FOREIGN KEY (equipment_id) REFERENCES equipment(equipment_id)
                                    )
                                ''')
                                
                                # Insert the battery record
                                cursor.execute('''
                                    INSERT INTO batteries (
                                        equipment_id, last_change_date, life_months
                                    ) VALUES (?, ?, ?)
                                ''', (
                                    equipment_id,
                                    equipment_data.get('battery_last_change_date', ''),
                                    equipment_data.get('battery_life', 0)
                                ))
                                logger.info(f"Added battery data for equipment {equipment_id}")
                            except Exception as e:
                                logger.error(f"Error saving battery data: {e}")
                        
                        # Check for discard criteria data and insert if present
                        if any(key in equipment_data for key in ['criteria_years', 'criteria_kms', 'criteria_hours', 'criteria_service_life', 'total_life']):
                            try:
                                # Create discard_criteria table if it doesn't exist
                                cursor.execute('''
                                    CREATE TABLE IF NOT EXISTS discard_criteria (
                                        criteria_id INTEGER PRIMARY KEY AUTOINCREMENT,
                                        equipment_id INTEGER,
                                        years INTEGER,
                                        kms REAL,
                                        hours REAL,
                                        service_life TEXT,
                                        total_life TEXT,
                                        FOREIGN KEY (equipment_id) REFERENCES equipment(equipment_id)
                                    )
                                ''')
                                
                                # Insert the discard criteria record with expanded fields
                                cursor.execute('''
                                    INSERT INTO discard_criteria (
                                        equipment_id, years, kms, hours, service_life, total_life
                                    ) VALUES (?, ?, ?, ?, ?, ?)
                                ''', (
                                    equipment_id,
                                    equipment_data.get('criteria_years', 0),
                                    equipment_data.get('criteria_kms', 0),
                                    equipment_data.get('criteria_hours', 0),
                                    equipment_data.get('criteria_service_life', ''),
                                    equipment_data.get('total_life', '')
                                ))
                                logger.info(f"Added discard criteria for equipment {equipment_id}")
                            except Exception as e:
                                logger.error(f"Error saving discard criteria: {e}")
                        
                        conn.commit()
                        equipment_count += 1
                        logger.info(f"Saved equipment {equipment_count}: {equipment_data['make_and_type']}")
                        
                    except Exception as e:
                        logger.error(f"Error saving equipment: {e}")
                    finally:
                        conn.close()
                
            except Exception as e:
                logger.error(f"Error processing row {index} in {sheet_name}: {e}")
                continue
        
        # Log summary of operations
        total_processed = equipment_count + equipment_updated
        if equipment_updated > 0:
            logger.info(f"Equipment processing summary for {sheet_name}: {equipment_count} created, {equipment_updated} updated, {total_processed} total")

        return total_processed

    def _find_equipment_by_ba_number(self, ba_number: str) -> Optional[int]:
        """Find equipment ID by BA Number for update operations."""
        if not ba_number or str(ba_number).strip() == '':
            return None

        import sqlite3
        import config

        try:
            conn = sqlite3.connect(config.DB_PATH)
            cursor = conn.cursor()

            cursor.execute('SELECT equipment_id FROM equipment WHERE ba_number = ?', (ba_number,))
            result = cursor.fetchone()
            conn.close()

            if result:
                logger.info(f"Found existing equipment with BA Number {ba_number}: equipment_id {result[0]}")
                return result[0]
            return None

        except Exception as e:
            logger.error(f"Error finding equipment by BA Number {ba_number}: {e}")
            return None

    def _update_existing_equipment(self, equipment_id: int, equipment_data: Dict[str, Any], sheet_name: str) -> bool:
        """Update existing equipment record with new data from Excel."""
        import sqlite3
        import config

        try:
            conn = sqlite3.connect(config.DB_PATH)
            cursor = conn.cursor()

            # Update equipment record with all new data
            cursor.execute('''
                UPDATE equipment SET
                    serial_number = ?,
                    make_and_type = ?,
                    ba_number = ?,
                    units_held = ?,
                    vintage_years = ?,
                    meterage_kms = ?,
                    hours_run_total = ?,
                    km_hrs_run_previous_month = ?,
                    km_hrs_run_current_month = ?,
                    hours_run_previous_month = ?,
                    hours_run_current_month = ?,
                    is_active = ?,
                    remarks = ?,
                    date_of_commission = ?
                WHERE equipment_id = ?
            ''', (
                equipment_data.get('serial_number', f"AUTO_UPD_{equipment_id}"),
                equipment_data['make_and_type'],
                equipment_data.get('ba_number'),
                equipment_data.get('units_held', 1),
                equipment_data.get('vintage_years', 0),
                equipment_data.get('meterage_kms', 0),
                equipment_data.get('hours_run_total', 0),
                equipment_data.get('km_hrs_run_previous_month', 0),
                equipment_data.get('km_hrs_run_current_month', 0),
                equipment_data.get('hours_run_previous_month', 0),
                equipment_data.get('hours_run_current_month', 0),
                1,  # is_active
                equipment_data.get('remarks', f"Updated from {sheet_name}"),
                equipment_data.get('date_of_commission'),
                equipment_id
            ))

            # Update associated data (fluids, maintenance, etc.)
            self._update_associated_data(equipment_id, equipment_data, cursor)

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            logger.error(f"Error updating equipment {equipment_id}: {e}")
            return False

    def _update_associated_data(self, equipment_id: int, equipment_data: Dict[str, Any], cursor):
        """Update all associated data for the equipment (fluids, maintenance, etc.)."""
        try:
            # Update fluid data if present
            fluid_types = [
                ('engine_oil', 'ENG OIL'),
                ('transmission_oil', 'Transmission Oil'),
                ('differential_oil', 'Differential Oil'),
                ('hydraulic_oil', 'Hydraulic Oil'),
                ('hydraulic_fluid', 'Hydraulic Fluid'),
                ('coolant', 'Coolant'),
                ('brake_clutch', 'Brake & Clutch Fluid'),
                ('clutch_oil', 'Clutch Oil'),
                ('steering_oil', 'Steering Oil'),
                ('hub_oil', 'Hub Oil'),
                ('front_axle', 'Front Axle'),
                ('rear_axle', 'Rear Axle'),
                ('gear_box', 'Gear Box'),
                ('grease', 'Grease'),
                ('hub_grease', 'Hub Grease'),
                ('nipple_grease', 'Nipple Grease'),
                ('engine_clutch_grease', 'Engine Clutch Grease'),
                ('wheel_arms_grease', 'Wheel Arms Grease'),
                ('track_mech_grease', 'Track Mechanism Grease')
            ]

            # Update fluid records
            for fluid_key, fluid_name in fluid_types:
                capacity_key = f"{fluid_key}_capacity"
                grade_key = f"{fluid_key}_grade"
                last_change_key = f"{fluid_key}_last_change_date"
                periodicity_key = f"{fluid_key}_periodicity"
                top_up_key = f"{fluid_key}_top_up"

                # Check if we have any fluid data for this type
                if any(key in equipment_data for key in [capacity_key, grade_key, last_change_key, periodicity_key]):
                    try:
                        # Check if fluid record exists
                        cursor.execute('''
                            SELECT fluid_id FROM fluids
                            WHERE equipment_id = ? AND fluid_type = ?
                        ''', (equipment_id, fluid_name))

                        existing_fluid = cursor.fetchone()

                        capacity_val = equipment_data.get(capacity_key, 0)
                        grade_val = equipment_data.get(grade_key, '')
                        last_change_val = equipment_data.get(last_change_key, '')
                        periodicity_val = equipment_data.get(periodicity_key, '')
                        top_up_val = equipment_data.get(top_up_key, 0)

                        if existing_fluid:
                            # Update existing fluid
                            cursor.execute('''
                                UPDATE fluids SET
                                    assembly_name = ?,
                                    capacity = ?,
                                    grade = ?,
                                    last_change_date = ?,
                                    periodicity = ?,
                                    top_up = ?
                                WHERE fluid_id = ?
                            ''', (
                                fluid_name,
                                capacity_val,
                                grade_val,
                                last_change_val,
                                periodicity_val,
                                top_up_val,
                                existing_fluid[0]
                            ))
                            logger.info(f"Updated {fluid_name} data for equipment {equipment_id}")
                        else:
                            # Insert new fluid if data is meaningful
                            if capacity_val > 0 or grade_val:
                                cursor.execute('''
                                    INSERT INTO fluids (
                                        equipment_id, fluid_type, assembly_name, capacity, grade,
                                        last_change_date, periodicity, top_up
                                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                                ''', (
                                    equipment_id,
                                    fluid_name,
                                    fluid_name,
                                    capacity_val,
                                    grade_val,
                                    last_change_val,
                                    periodicity_val,
                                    top_up_val
                                ))
                                logger.info(f"Added new {fluid_name} data for equipment {equipment_id}")
                    except Exception as e:
                        logger.error(f"Error updating fluid data {fluid_name} for equipment {equipment_id}: {e}")

            logger.info(f"Updated associated data for equipment {equipment_id}")

        except Exception as e:
            logger.error(f"Error updating associated data for equipment {equipment_id}: {e}")
    
    def _map_equipment_columns(self, columns: list) -> Dict[str, str]:
        """Map DataFrame columns to equipment fields with enhanced unnamed column handling.

        Handles both standard column names and composite names like 'FLUID TYPE -> ATTRIBUTE'.
        Filters out unnamed, empty, and malformed columns.
        Returns a dictionary mapping field names to column names.
        """
        col_map = {}

        # Filter out problematic columns before mapping
        valid_columns = []
        filtered_count = 0

        for col in columns:
            col_str = str(col).strip()

            # Skip unnamed columns
            if ('Unnamed:' in col_str or col_str.startswith('Unnamed') or
                col_str.startswith('SKIP_COLUMN_') or
                col_str in ['', 'nan', 'NaN', 'None', 'null']):
                filtered_count += 1
                logger.debug(f"Filtered out problematic column: '{col_str}'")
                continue

            # Skip numeric-only column names (likely malformed)
            if col_str.replace('.', '').replace('-', '').isdigit():
                filtered_count += 1
                logger.debug(f"Filtered out numeric column: '{col_str}'")
                continue

            # Skip very short column names (likely malformed)
            if len(col_str) < 2:
                filtered_count += 1
                logger.debug(f"Filtered out short column: '{col_str}'")
                continue

            valid_columns.append(col)

        logger.info(f"Column filtering: {len(columns)} original -> {len(valid_columns)} valid ({filtered_count} filtered out)")
        logger.info(f"Valid columns to map: {valid_columns}")
        logger.info(f"Starting column mapping process for {len(valid_columns)} valid columns")
        
        # Define fluid field mappings
        fluid_fields_map = {
            'ENG OIL': {
                'CAPACITY': 'engine_oil_capacity',
                'GRADE': 'engine_oil_grade',
                'DT OF CHANGE': 'engine_oil_last_change_date',
                'PERIODICITY': 'engine_oil_periodicity',
                'ADDL 10% TOP UP': 'engine_oil_top_up'
            },
            'HYDRAULIC FLUID': {
                'CAPACITY': 'hydraulic_fluid_capacity',
                'GRADE': 'hydraulic_fluid_grade',
                'DT OF CHANGE': 'hydraulic_fluid_last_change_date',
                'PERIODICITY': 'hydraulic_fluid_periodicity',
                'ADDL 10% TOP UP': 'hydraulic_fluid_top_up'
            },
            'COOLANT': {
                'CAPACITY': 'coolant_capacity',
                'GRADE': 'coolant_grade',
                'DT OF CHANGE': 'coolant_last_change_date',
                'PERIODICITY': 'coolant_periodicity',
                'ADDL 10% TOP UP': 'coolant_top_up'
            },
            'TRANSMISSION OIL': {
                'CAPACITY': 'transmission_oil_capacity',
                'GRADE': 'transmission_oil_grade',
                'DT OF CHANGE': 'transmission_oil_last_change_date',
                'PERIODICITY': 'transmission_oil_periodicity',
                'ADDL 10% TOP UP': 'transmission_oil_top_up'
            },
            'GEAR BOX': {
                'CAPACITY': 'gear_box_capacity',
                'GRADE': 'gear_box_grade',
                'DT OF CHANGE': 'gear_box_last_change_date',
                'PERIODICITY': 'gear_box_periodicity',
                'ADDL 10% TOP UP': 'gear_box_top_up'
            },
            'GEAR BOX (TXN OIL)': {
                'CAPACITY': 'gear_box_txn_capacity',
                'GRADE': 'gear_box_txn_grade',
                'DT OF CHANGE': 'gear_box_txn_last_change_date',
                'PERIODICITY': 'gear_box_txn_periodicity',
                'ADDL 10% TOP UP': 'gear_box_txn_top_up'
            },
            'FRONT AXLE': {
                'CAPACITY': 'front_axle_capacity',
                'GRADE': 'front_axle_grade',
                'DT OF CHANGE': 'front_axle_last_change_date',
                'PERIODICITY': 'front_axle_periodicity',
                'ADDL 10% TOP UP': 'front_axle_top_up'
            },
            'REAR AXLE': {
                'CAPACITY': 'rear_axle_capacity',
                'GRADE': 'rear_axle_grade',
                'DT OF CHANGE': 'rear_axle_last_change_date',
                'PERIODICITY': 'rear_axle_periodicity',
                'ADDL 10% TOP UP': 'rear_axle_top_up'
            },
            'BRAKE FLUID': {
                'CAPACITY': 'brake_fluid_capacity',
                'GRADE': 'brake_fluid_grade',
                'DT OF CHANGE': 'brake_fluid_last_change_date',
                'PERIODICITY': 'brake_fluid_periodicity',
                'ADDL 10% TOP UP': 'brake_fluid_top_up'
            },
            'CLUTCH OIL': {
                'CAPACITY': 'clutch_oil_capacity',
                'GRADE': 'clutch_oil_grade',
                'DT OF CHANGE': 'clutch_oil_last_change_date',
                'PERIODICITY': 'clutch_oil_periodicity',
                'ADDL 10% TOP UP': 'clutch_oil_top_up'
            },
            'STEERING OIL': {
                'CAPACITY': 'steering_oil_capacity',
                'GRADE': 'steering_oil_grade',
                'DT OF CHANGE': 'steering_oil_last_change_date',
                'PERIODICITY': 'steering_oil_periodicity',
                'ADDL 10% TOP UP': 'steering_oil_top_up'
            },
            'HUB OIL': {
                'CAPACITY': 'hub_oil_capacity',
                'GRADE': 'hub_oil_grade',
                'DT OF CHANGE': 'hub_oil_last_change_date',
                'PERIODICITY': 'hub_oil_periodicity',
                'ADDL 10% TOP UP': 'hub_oil_top_up'
            },
            'HUB GREASE': {
                'CAPACITY': 'hub_grease_capacity',
                'GRADE': 'hub_grease_grade',
                'DT OF CHANGE': 'hub_grease_last_change_date',
                'PERIODICITY': 'hub_grease_periodicity',
                'ADDL 10% TOP UP': 'hub_grease_top_up'
            },
            'NIPPLE GREASE': {
                'CAPACITY': 'nipple_grease_capacity',
                'GRADE': 'nipple_grease_grade',
                'DT OF CHANGE': 'nipple_grease_last_change_date',
                'PERIODICITY': 'nipple_grease_periodicity',
                'ADDL 10% TOP UP': 'nipple_grease_top_up'
            },
            'GREASE ENGINE CLUTCH': {
                'CAPACITY': 'engine_clutch_grease_capacity',
                'GRADE': 'engine_clutch_grease_grade',
                'DT OF CHANGE': 'engine_clutch_grease_last_change_date',
                'PERIODICITY': 'engine_clutch_grease_periodicity',
                'ADDL 10% TOP UP': 'engine_clutch_grease_top_up'
            },
            'GREASE': {
                'CAPACITY': 'grease_capacity',
                'GRADE': 'grease_grade',
                'DT OF CHANGE': 'grease_last_change_date',
                'PERIODICITY': 'grease_periodicity',
                'ADDL 10% TOP UP': 'grease_top_up'
            },
            'BRAKE & CLUTCH': {
                'CAPACITY': 'brake_clutch_capacity',
                'GRADE': 'brake_clutch_grade',
                'DT OF CHANGE': 'brake_clutch_last_change_date',
                'PERIODICITY': 'brake_clutch_periodicity',
                'ADDL 10% TOP UP': 'brake_clutch_top_up'
            },
            'HYD OIL': {
                'CAPACITY': 'hyd_oil_capacity',
                'GRADE': 'hyd_oil_grade',
                'DT OF CHANGE': 'hyd_oil_last_change_date',
                'PERIODICITY': 'hyd_oil_periodicity',
                'ADDL 10% TOP UP': 'hyd_oil_top_up'
            },
            'DIFFERENTIAL': {
                'CAPACITY': 'differential_capacity',
                'GRADE': 'differential_grade',
                'DT OF CHANGE': 'differential_last_change_date',
                'PERIODICITY': 'differential_periodicity',
                'ADDL 10% TOP UP': 'differential_top_up'
            },
            'TXN OIL': {
                'CAPACITY': 'txn_oil_capacity',
                'GRADE': 'txn_oil_grade',
                'DT OF CHANGE': 'txn_oil_last_change_date',
                'PERIODICITY': 'txn_oil_periodicity',
                'ADDL 10% TOP UP': 'txn_oil_top_up'
            },
            'CLUTCH': {
                'CAPACITY': 'clutch_capacity',
                'GRADE': 'clutch_grade',
                'DT OF CHANGE': 'clutch_last_change_date',
                'PERIODICITY': 'clutch_periodicity',
                'ADDL 10% TOP UP': 'clutch_top_up'
            },
            'ROAD WHEELS ARMS': {
                'CAPACITY': 'road_wheels_arms_capacity',
                'GRADE': 'road_wheels_arms_grade',
                'DT OF CHANGE': 'road_wheels_arms_last_change_date',
                'PERIODICITY': 'road_wheels_arms_periodicity',
                'ADDL 10% TOP UP': 'road_wheels_arms_top_up'
            },
            'ROAD WHEELS ARMS IDLER WHEEL & TRACK ADJUSTING MECHANISM GREASE': {
                'CAPACITY': 'track_mech_grease_capacity',
                'GRADE': 'track_mech_grease_grade',
                'DT OF CHANGE': 'track_mech_grease_last_change_date',
                'PERIODICITY': 'track_mech_grease_periodicity',
                'ADDL 10% TOP UP': 'track_mech_grease_top_up'
            },
            'GREASE ENGINE CLUTCH RELEASE MECHANISM': {
                'CAPACITY': 'clutch_release_grease_capacity',
                'GRADE': 'clutch_release_grease_grade',
                'DT OF CHANGE': 'clutch_release_grease_last_change_date',
                'PERIODICITY': 'clutch_release_grease_periodicity',
                'ADDL 10% TOP UP': 'clutch_release_grease_top_up'
            },
            'TRANSMISSION SYS': {
                'CAPACITY': 'transmission_sys_capacity',
                'GRADE': 'transmission_sys_grade',
                'DT OF CHANGE': 'transmission_sys_last_change_date',
                'PERIODICITY': 'transmission_sys_periodicity',
                'ADDL 10% TOP UP': 'transmission_sys_top_up'
            }
        }
        
        # Equipment basic field mappings - these should match the primary columns directly
        primary_mappings = {
            'make_and_type': [
                r'^MAKE & TYPE$', r'^make\s*&\s*type$', r'^make\s*and\s*type$',
                r'equipment.*type', r'vehicle.*type', r'make', r'type', 
                r'equipment', r'vehicle', r'description'
            ],
            'ba_number': [
                r'^BA NO$', r'^ba.*no', r'^ba.*number', r'army.*no', r'registration.*no', r'reg.*no'
            ],
            'serial_number': [
                r'^SER NO$', r'^serial.*number', r'^serial.*no', r'^serial', r'^s.*no', r'^sl.*no'
            ],
            'units_held': [
                r'units', r'qty', r'quantity', r'count', r'units.*held',
                r'units.*available', r'held'
            ],
            'meterage_kms': [
                r'^KM RUN$', r'^km.*run', r'^kms.*run', r'^meterage', r'^km$', r'^kms$', 
                r'kilometers', r'mileage', r'total.*km'
            ],
            'hours_run_total': [
                r'^HRS RUN$', r'^hrs.*run', r'^hours.*run', r'^hrs\s*run', r'^hours\s*run',
                r'runtime', r'engine.*hours', r'total.*hours', r'running.*hours'
            ],
            'vintage_years': [
                r'^VINTAGE$', r'^vintage', r'age.*years', r'years.*old', r'years.*service'
            ],
            'date_of_commission': [
                r'^DATE OF REL$', r'^date.*commission', r'commission.*date', r'^date.*release',
                r'date.*rel', r'release.*date'
            ],
            'remarks': [
                r'^REMARKS$', r'^remarks', r'^comment', r'^notes'
            ]
        }
        
        # First pass: Map primary equipment fields using valid columns
        for field, patterns in primary_mappings.items():
            for col in valid_columns:
                col_str = str(col)
                col_lower = col_str.lower()

                for pattern in patterns:
                    if re.search(pattern, col_lower, re.IGNORECASE):
                        col_map[field] = col
                        logger.info(f"Mapped primary field '{field}' to column: '{col}'")
                        break
                if field in col_map:
                    break
                
        # Second pass: Map fluid and maintenance related fields from composite columns
        # We expect these to be in format "FLUID TYPE -> ATTRIBUTE"  
        fluid_fields_map = {}
        
        # Special fields like overhauls and maintenance
        special_fields_map = {
            'OH-I DONE': 'overhaul_i_done',
            'OH-I DUE': 'overhaul_i_due',
            'OH-II DONE': 'overhaul_ii_done',
            'OH-II DUE': 'overhaul_ii_due',
            'TM-I DONE': 'technical_maintenance_i_done',
            'TM-I DUE': 'technical_maintenance_i_due',
            'TM-II DONE': 'technical_maintenance_ii_done',
            'TM-II DUE': 'technical_maintenance_ii_due',
            'TM - I DONE': 'technical_maintenance_i_done',
            'TM - I DUE': 'technical_maintenance_i_due',
            'TM - II DONE': 'technical_maintenance_ii_done',
            'TM - II DUE': 'technical_maintenance_ii_due',
            'DONE': 'maintenance_done',
            'DUE': 'maintenance_due',
            'BOH': 'break_out_holding',
            'BOH (8-10 Yrs)': 'break_out_holding',
            'BOH (8-10 YRS)': 'break_out_holding',
            # Tyre fields
            'TYRE -> ROTATION': 'tyre_rotation_kms',
            'TYRE -> DT OF CHANGE': 'tyre_last_change_date',
            'TYRE -> QTY': 'tyre_quantity',
            'ROTATION': 'tyre_rotation_kms',
            'ROTATION (5000 KM)': 'tyre_rotation_kms',
            'TYRE CONDITION': 'tyre_condition',
            'TYRE CONDITION (20000 KM)': 'tyre_condition',
            'DT OF CHANGE': 'tyre_last_change_date', 
            'LIFE': 'tyre_life',
            # Battery fields
            'BTY -> DT OF CHANGE': 'battery_last_change_date',
            'BTY -> LIFE': 'battery_life',
            'BTY': 'battery_quantity',
            'BTY (85 AH)': 'battery_quantity',
            # Discard criteria fields
            'DISCARD CRITERIA -> VINTAGE': 'criteria_years',
            'DISCARD CRITERIA -> METERAGE': 'criteria_kms',
            'VINTAGE (YEARS)': 'criteria_years',
            'VINTAGE YEARS': 'criteria_years',
            'METERAGE (KMS)': 'criteria_kms',
            'METERAGE KMS': 'criteria_kms',
            'HR RUN (7000 Hr)': 'criteria_hours',
            'HR RUN': 'criteria_hours',
            'HR RUN (7000 HRS)': 'criteria_hours',
            'SERVICE LIFE (20 YRS)': 'criteria_service_life',
            'TOTAL LIFE': 'total_life'
        }
        
        # Process composite columns (for fluids) using valid columns only
        for col in valid_columns:
            col_str = str(col)
            logger.debug(f"Processing valid column: {col_str}")

            # Handle different separator patterns for composite columns
            separator_found = False
            fluid_type = None
            attribute = None
            
            # Check if this is directly a fluid column without separators
            # Examples: "ENG OIL", "COOLANT", etc.
            for fluid_key in fluid_fields_map.keys():
                if fluid_key == col_str.upper() or fluid_key in col_str.upper():
                    logger.info(f"Found direct fluid column: {col_str} matching {fluid_key}")
                    # Default to capacity if no attribute is specified
                    field_name = fluid_fields_map[fluid_key]['CAPACITY']
                    col_map[field_name] = col
                    break
            
            # Try different separator patterns
            for separator in [' -> ', ' : ', ' - ', ' >', ':', '>', '-', '.', ' ']:
                if separator in col_str:
                    parts = col_str.split(separator, 1)
                    if len(parts) == 2:
                        fluid_type = parts[0].strip().upper()
                        attribute = parts[1].strip().upper()
                        separator_found = True
                        logger.info(f"Found composite column with separator '{separator}': {fluid_type} | {attribute}")
                        break
            
            # Enhanced ASSEMBLY naming handling which might be used in the UI
            if (fluid_type == 'ASSEMBLY' or 'ASSEMBLY' in col_str.upper()) and attribute:
                # This is likely a fluid type with ASSEMBLY as the category
                # Try to map the attribute as the actual fluid type
                logger.info(f"Found ASSEMBLY column: {attribute}")
                
                # Try more flexible matching for fluid types in assembly naming
                for fluid_key, fluid_map in fluid_fields_map.items():
                    # Convert fluid key to common terms for more flexible matching
                    search_terms = [
                        fluid_key,  # e.g., 'ENG OIL'
                        fluid_key.replace('_', ' '),  # e.g., 'ENGINE OIL'
                        fluid_key.replace(' ', ''),   # e.g., 'ENGOIL'
                        fluid_key.split()[0] if ' ' in fluid_key else fluid_key  # e.g., 'ENG' or 'ENGINE'
                    ]
                    
                    # Check various forms of the attribute name for matches
                    attr_upper = attribute.upper()
                    if any(term.upper() in attr_upper or attr_upper in term.upper() for term in search_terms):
                        logger.info(f"Mapped ASSEMBLY {attribute} to fluid type {fluid_key}")
                        fluid_type = fluid_key
                        
                        # Try to determine the attribute type (capacity, grade, etc.)
                        attribute_types = [
                            ('CAPACITY', ['CAPACITY', 'CAP', 'VOLUME', 'QTY', 'LTRS', 'LITERS']),
                            ('GRADE', ['GRADE', 'TYPE', 'SPEC', 'SPECIFICATION']),
                            ('DT OF CHANGE', ['DT OF CHANGE', 'LAST', 'DATE', 'REPLACEMENT', 'REPLACED']),
                            ('PERIODICITY', ['PERIODICITY', 'INTERVAL', 'PERIOD', 'FREQUENCY', 'CYCLE'])
                        ]
                        
                        for attr_name, keywords in attribute_types:
                            if any(keyword in col_str.upper() for keyword in keywords):
                                attribute = attr_name
                                logger.info(f"Determined attribute {attribute} for {fluid_type}")
                                break
                        break
            
            # Check if this is a known fluid type and attribute combination
            if separator_found and fluid_type in fluid_fields_map:
                # Enhanced attribute matching for standard fluid attributes structure
                # Standard attributes for each fluid type in Excel
                standard_attributes = {
                    'CAPACITY LTRS/KG': 'CAPACITY',
                    'CAPACITY': 'CAPACITY',
                    'ADDL 10% TOP UP': 'ADDL 10% TOP UP',
                    'GRADE': 'GRADE',
                    'DT OF CHANGE': 'DT OF CHANGE',
                    'PERIODICITY HRS/MONTH': 'PERIODICITY',
                    'PERIODICITY': 'PERIODICITY'
                }
                
                # Try exact match first
                if attribute in fluid_fields_map[fluid_type]:
                    field_name = fluid_fields_map[fluid_type][attribute]
                    col_map[field_name] = col
                    logger.info(f"Direct matched fluid field '{field_name}' to column: '{col}'")
                elif attribute in standard_attributes and standard_attributes[attribute] in fluid_fields_map[fluid_type]:
                    # Map standard attribute to known field name
                    field_name = fluid_fields_map[fluid_type][standard_attributes[attribute]]
                    col_map[field_name] = col
                    logger.info(f"Standard attribute matched fluid field '{field_name}' to column: '{col}'")
                else:
                    # More flexible attribute matching with common terms
                    attribute_groups = {
                        'CAPACITY': ['CAPACITY', 'CAP', 'VOLUME', 'QTY', 'LTRS', 'LITERS', 'QUANT', 'AMOUNT', 'LTRS/KG'],
                        'GRADE': ['GRADE', 'TYPE', 'SPEC', 'SPECIFICATION', 'BRAND', 'QUALITY'],
                        'DT OF CHANGE': ['CHANGE', 'LAST', 'DATE', 'REPLACEMENT', 'CHANGED', 'REPLACED', 'RECENT', 'DT OF CHANGE'],
                        'PERIODICITY': ['PERIODICITY', 'INTERVAL', 'PERIOD', 'FREQUENCY', 'CYCLE', 'ROUTINE', 'HRS/MONTH', 'KM/HRS', 'MONTHS'],
                        'ADDL 10% TOP UP': ['ADDL 10%', 'TOP UP', 'ADDITIONAL', 'EXTRA', '10%']
                    }
                    
                    # Try to match attribute to a known group
                    matched = False
                    for main_attr, keywords in attribute_groups.items():
                        if any(keyword in attribute for keyword in keywords) or any(keyword in attribute.replace(' ', '') for keyword in keywords):
                            if main_attr in fluid_fields_map[fluid_type]:
                                field_name = fluid_fields_map[fluid_type][main_attr]
                                col_map[field_name] = col
                                logger.info(f"Group matched fluid field '{field_name}' to column: '{col}' via '{main_attr}'")
                                matched = True
                                break
                    
                    # If no group match, try the original partial matching
                    if not matched:
                        for attr_key, field_value in fluid_fields_map[fluid_type].items():
                            if attr_key in attribute or attribute in attr_key:
                                field_name = field_value
                                col_map[field_name] = col
                                logger.info(f"Partially matched fluid field '{field_name}' to column: '{col}'")
                                matched = True
                                break
                    
                    # Last resort - add with some heuristic guessing
                    if not matched and len(attribute) > 3:
                        # Try to guess based on common patterns
                        if any(word in attribute for word in ['VOL', 'QTY', 'CAP', 'LTR', 'AMOUNT']):
                            field_name = fluid_fields_map[fluid_type].get('CAPACITY', fluid_fields_map[fluid_type].get('CAPACITY LTRS/KG'))
                            if field_name:
                                col_map[field_name] = col
                                logger.info(f"Heuristic-matched fluid capacity '{field_name}' to column: '{col}'")
                        elif any(word in attribute for word in ['SPEC', 'BRAND', 'TYPE', 'GRADE']):
                            field_name = fluid_fields_map[fluid_type].get('GRADE')
                            if field_name:
                                col_map[field_name] = col
                                logger.info(f"Heuristic-matched fluid grade '{field_name}' to column: '{col}'")
                
                # Check if this is a special composite field
                full_key = col_str.upper()
                for pattern, field_name in special_fields_map.items():
                    if re.search(pattern, full_key, re.IGNORECASE):
                        col_map[field_name] = col
                        logger.info(f"Mapped special field '{field_name}' to column: '{col}'")
                        break
        
        logger.info(f"Final column mapping (found {len(col_map)} fields): {col_map}")
        return col_map
    
    def _normalize_column_name(self, col_name: str) -> str:
        """Normalize column name for matching."""
        if not col_name:
            return ''
        return re.sub(r'[^a-z0-9]', '', str(col_name).lower().replace(' ', ''))
    
    def _extract_equipment_data(self, row: pd.Series, col_map: Dict[str, str], sheet_name: str) -> Dict[str, Any]:
        """Extract equipment data from a DataFrame row."""
        data = {}
        
        # Debug logging
        logger.info(f"Extracting data for row in {sheet_name}")
        logger.info(f"Column mapping: {col_map}")
        
        # Primary numeric fields for equipment
        numeric_fields = ['meterage_kms', 'hours_run_total', 'units_held']  # Removed vintage_years - will calculate it
        
        # Date fields
        date_fields = [
            'date_of_commission', 'date_of_induction',
            'oh1_done_date', 'oh1_due_date', 'oh2_done_date', 'oh2_due_date',
            'tm1_done_date', 'tm1_due_date', 'tm2_done_date', 'tm2_due_date',
            'engine_oil_last_change_date', 'transmission_oil_last_change_date',
            'differential_oil_last_change_date', 'hydraulic_oil_last_change_date',
            'coolant_last_change_date', 'brake_clutch_last_change_date',
            'tyre_last_change_date', 'battery_last_change_date'
        ]
        
        # Fluid capacity fields
        capacity_fields = [
            'engine_oil_capacity', 'transmission_oil_capacity', 'differential_oil_capacity', 
            'hydraulic_oil_capacity', 'coolant_capacity', 'brake_clutch_capacity'
        ]
        
        # Extract mapped fields
        for field, col_name in col_map.items():
            if col_name in row.index:
                value = row[col_name]
                logger.info(f"Field '{field}' -> Column '{col_name}' -> Value: '{value}'")
                if pd.notna(value) and str(value).strip() and str(value).strip() not in ['-', 'NA', 'N/A']:
                    # Handle numeric fields
                    if field in numeric_fields or field in capacity_fields or 'periodicity' in field:
                        data[field] = self._parse_numeric(value)
                        logger.info(f"Set numeric {field} = {data[field]}")
                    # Handle date fields
                    elif field in date_fields or field.endswith('_date'):
                        data[field] = self._parse_date(value)
                        logger.info(f"Set date {field} = {data[field]}")
                    # Skip vintage_years from Excel - we'll calculate it from date
                    elif field == 'vintage_years':
                        logger.info(f"Skipping vintage_years from Excel - will calculate from date_of_commission")
                        continue
                    # Handle all other fields as strings
                    else:
                        data[field] = str(value).strip()
                        logger.info(f"Set {field} = '{data[field]}'")
            else:
                logger.warning(f"Column '{col_name}' not found in row index for field '{field}'")
        
        # CALCULATE VINTAGE_YEARS FROM DATE_OF_COMMISSION (DATE OF REL)
        if 'date_of_commission' in data and data['date_of_commission']:
            try:
                from datetime import datetime, date
                release_date_str = data['date_of_commission']
                
                # Parse the release date
                if isinstance(release_date_str, str):
                    release_date = datetime.strptime(release_date_str, '%Y-%m-%d').date()
                else:
                    release_date = release_date_str
                
                # Calculate vintage years from release date to current date
                current_date = date.today()
                years_diff = current_date.year - release_date.year
                
                # Adjust for partial years (if birthday hasn't occurred this year)
                if (current_date.month, current_date.day) < (release_date.month, release_date.day):
                    years_diff -= 1
                
                data['vintage_years'] = max(0, years_diff)  # Ensure non-negative
                logger.info(f"Calculated vintage_years = {data['vintage_years']} from release date {release_date_str}")
                
            except Exception as e:
                logger.warning(f"Could not calculate vintage_years from date_of_commission '{data['date_of_commission']}': {e}")
                data['vintage_years'] = 0
        else:
            # If no date_of_commission, try to get vintage_years from Excel as fallback
            if 'vintage_years' in col_map:
                col_name = col_map['vintage_years']
                if col_name in row.index:
                    value = row[col_name]
                    if pd.notna(value) and str(value).strip() and str(value).strip() not in ['-', 'NA', 'N/A']:
                        data['vintage_years'] = self._parse_numeric(value)
                        logger.info(f"Used Excel vintage_years = {data['vintage_years']} (no date_of_commission available)")
                    else:
                        data['vintage_years'] = 0
                else:
                    data['vintage_years'] = 0
            else:
                data['vintage_years'] = 0
        
        # If no make_and_type found in mapped columns, try to find it in any column
        if 'make_and_type' not in data:
            logger.info(f"No make_and_type found in mapped data, searching all columns...")
            equipment_keywords = [
                'tatra', 'truck', 'vehicle', 'generator', 'jcb', 'dozer', 
                'bmp', 'tank', 'bulldozer', 'excavator', 'crane', 'trailer',
                'jeep', 'bus', 'ambulance', 'recovery', 'workshop', 'water',
                'fuel', 'cargo', 'personnel', 'command', 'communication'
            ]
            
            # Look for equipment names in any column
            for col in row.index:
                value = row[col]
                if pd.notna(value) and str(value).strip():
                    val_str = str(value).strip()
                    val_lower = val_str.lower()
                    
                    # Check if this looks like an equipment name
                    if (len(val_str) > 3 and 
                        (any(keyword in val_lower for keyword in equipment_keywords) or
                         re.search(r'\d+x\d+', val_lower) or  # Pattern like 6X6, 8X8
                         re.search(r'\d+\s*cyl', val_lower) or  # Pattern like 12 cyl
                         'cabin' in val_lower)):
                        
                        # Clean up the equipment name
                        cleaned_name = re.sub(r'\s+', ' ', val_str)
                        data['make_and_type'] = cleaned_name
                        logger.info(f"Found equipment name in column '{col}': {cleaned_name}")
                        break
            
            # If still no equipment type found, look for any meaningful text
            if 'make_and_type' not in data:
                logger.info(f"Still no equipment name found, trying any meaningful text...")
                for col in row.index:
                    value = row[col]
                    if pd.notna(value) and str(value).strip():
                        val_str = str(value).strip()
                        # Use the first non-trivial text value
                        if len(val_str) > 5 and not val_str.lower() in ['unnamed', 'nan', 'null']:
                            data['make_and_type'] = val_str
                            logger.info(f"Using first meaningful text as equipment name: {val_str}")
                            break
            
            # Fallback: use sheet name if still no equipment type found
            if 'make_and_type' not in data:
                data['make_and_type'] = f"Equipment from {sheet_name}"
                logger.warning(f"Using fallback name: {data['make_and_type']}")
        
        logger.info(f"Final extracted data: {data}")
        return data
    
    def _is_valid_equipment_record(self, equipment_data: Dict[str, Any]) -> bool:
        """Check if the equipment record is valid and not a header/validation row."""
        make_and_type = equipment_data.get('make_and_type', '').strip().upper()
        ba_number = equipment_data.get('ba_number', '').strip().upper()
        serial_number = equipment_data.get('serial_number', '').strip().upper()
        
        # Skip records with validation text
        invalid_indicators = [
            'NOT ASSIGNED',
            'ALL DATA ARE CORRECT',
            'ALL DATA CORRECT',
            'HEADER',
            'TOTAL',
            'SUMMARY',
            'VALIDATION',
            'CHECK',
            'VERIFY'
        ]
        
        # Check make_and_type for invalid indicators
        for indicator in invalid_indicators:
            if indicator in make_and_type:
                logger.info(f"Skipping invalid record: {make_and_type}")
                return False
        
        # Check BA number for invalid indicators
        for indicator in invalid_indicators:
            if indicator in ba_number:
                logger.info(f"Skipping invalid record with BA: {ba_number}")
                return False
        
        # Check serial number for invalid indicators
        for indicator in invalid_indicators:
            if indicator in serial_number:
                logger.info(f"Skipping invalid record with Serial: {serial_number}")
                return False
        
        # Skip records where make_and_type is just the sheet name (fallback case)
        if make_and_type.startswith('EQUIPMENT FROM'):
            # Check if it's a meaningful equipment type or just a generic fallback
            meaningful_keywords = [
                'BMP', 'TATRA', 'TRUCK', 'TANK', 'VEHICLE', 'GENERATOR', 
                'JCB', 'DOZER', 'CRANE', 'TRAILER', 'ALS', 'MSS', 'CT'
            ]
            has_meaningful_content = any(keyword in make_and_type for keyword in meaningful_keywords)
            if not has_meaningful_content:
                logger.info(f"Skipping generic fallback record: {make_and_type}")
                return False
        
        # Additional validation: skip if all numeric fields are zero
        numeric_fields = ['meterage_kms', 'vintage_years']
        all_zero = True
        for field in numeric_fields:
            if equipment_data.get(field, 0) > 0:
                all_zero = False
                break
        
        if all_zero and not ba_number:  # No BA number and all zeros is suspicious
            logger.info(f"Skipping record with all zero values and no BA number: {make_and_type}")
            return False
        
        return True
    
    def _extract_and_save_fluids(self, df: pd.DataFrame, sheet_name: str) -> int:
        """Extract fluids data from DataFrame and save to database."""
        fluids_count = 0
        
        # Find fluid-related columns
        fluid_types = ['ENG OIL', 'HYDRAULIC FLUID', 'COOLANT', 'GREASE ENGINE CLUTCH', 
                      'ROAD WHEELS ARMS', 'BRAKE FLUID', 'TRANSMISSION OIL', 'GEAR BOX', 'GEAR BOX (TXN OIL)',
                      'REAR AXLE', 'FRONT AXLE', 'HUB GREASE', 'NIPPLE GREASE', 'STEERING OIL', 'ROAD WHEELS ARMS IDLER WHEEL & TRACK ADJUSTING MECHANISM GREASE',
                      'GREASE ENGINE CLUTCH RELEASE MECHANISM', 'CLUTCH', 'GREASE', 'CLUTCH OIL', 'BRAKE & CLUTCH', 'HYD OIL', 'HUB OIL', 'DIFFERENTIAL', 'TXN OIL', 'TRANSMISSION SYS']
        
        # Map equipment data first to get equipment IDs
        equipment_col_map = self._map_equipment_columns(df.columns)
        
        # Pre-scan the Excel for fluid columns to determine which fluid types actually exist
        fluid_columns_by_type = {}
        for fluid_type in fluid_types:
            fluid_columns_by_type[fluid_type] = []
        
        # First, identify all column headers that match each fluid type
        for col in df.columns:
            col_str = str(col).upper()
            
            # Check each fluid type for a match in this column
            for fluid_type in fluid_types:
                fluid_type_upper = fluid_type.upper()
                
                # Method 1: Exact match for the fluid type
                if col_str == fluid_type_upper:
                    fluid_columns_by_type[fluid_type].append(col)
                    continue
                    
                # Method 2: Fluid type followed by separator and attribute
                separators = [' -> ', ' : ', ' - ', ' >', ':', '>', '-', '.', ' ']
                for separator in separators:
                    if separator in col_str:
                        parts = col_str.split(separator, 1)
                        if len(parts) == 2 and parts[0].strip() == fluid_type_upper:
                            fluid_columns_by_type[fluid_type].append(col)
                            break
        
        # Process each row
        for idx, row in df.iterrows():
            try:
                equipment_data = self._extract_equipment_data(row, equipment_col_map, sheet_name)
                equipment_id = self._find_equipment_id(equipment_data)
                
                if not equipment_id:
                    logger.warning(f"No equipment ID found for row {idx+2} - skipping fluid extraction")
                    continue
                    
                logger.info(f"Extracting fluid data for equipment ID: {equipment_id} (BA Number: {equipment_data.get('ba_number', 'Unknown')})")
                
                # Only extract fluid types that have data for this equipment
                for fluid_type in fluid_types:
                    # Skip if there are no columns for this fluid type
                    if not fluid_columns_by_type[fluid_type]:
                        continue
                        
                    # Implement stronger filtering - check if this equipment row has meaningful values for this fluid type
                    has_data = False
                    has_capacity = False
                    has_grade = False
                    has_date = False
                    
                    # Check for specific data attributes that make a fluid entry meaningful
                    for col in fluid_columns_by_type[fluid_type]:
                        col_str = str(col).upper()
                        value = row[col]
                        
                        # Skip empty values
                        if pd.isna(value) or str(value).strip() == '':
                            continue
                            
                        # Count capacity-related data
                        if any(term in col_str for term in ['CAPACITY', 'LTRS', 'KG', 'VOL']):
                            if self._parse_numeric(value) > 0:
                                has_capacity = True
                                has_data = True
                        
                        # Count grade information
                        elif any(term in col_str for term in ['GRADE', 'SPEC', 'TYPE']):
                            if isinstance(value, str) and value.strip() != '':
                                has_grade = True
                                has_data = True
                        
                        # Count date information
                        elif any(term in col_str for term in ['DATE', 'DT OF CHANGE', 'LAST', 'CHANGE']):
                            if value and str(value).strip() != '':
                                has_date = True
                                has_data = True
                        
                        # Any other non-empty value
                        else:
                            has_data = True
                    
                    # Skip this fluid type if no meaningful data OR no capacity data (most essential)
                    if not has_data or not has_capacity:
                        logger.debug(f"Skipping {fluid_type} for equipment {equipment_id} - insufficient data (has_capacity={has_capacity}, has_grade={has_grade}, has_date={has_date})")
                        continue
                        
                    logger.info(f"Found data for {fluid_type} on equipment {equipment_id} (BA Number: {equipment_data.get('ba_number', 'Unknown')})")
                    
                    # Extract fluid data
                    fluid_data = self._extract_fluid_data(row, fluid_type, equipment_id)
                    
                    if fluid_data and fluid_data.get('capacity_ltrs_kg', 0) > 0:
                        # Additional validation before saving to ensure we only save meaningful entries
                        ba_number = equipment_data.get('ba_number')
                        make_type = equipment_data.get('make_and_type')
                        
                        # Record which fluid types are saved for this equipment for debugging
                        equipment_key = f"{ba_number}_{make_type}" if ba_number else make_type
                        logger.info(f"For equipment {equipment_key}: Saving {fluid_type} with capacity {fluid_data.get('capacity_ltrs_kg')} and grade {fluid_data.get('grade')}")
                        
                        if self._save_fluid_to_db(fluid_data):
                            fluids_count += 1
                            logger.info(f"Successfully saved {fluid_type} data for equipment ID {equipment_id} (BA: {ba_number})")
                        else:
                            logger.warning(f"Failed to save {fluid_type} data for equipment ID {equipment_id} (BA: {ba_number})")
                    
            except Exception as e:
                logger.error(f"Error processing fluids for row {idx} in {sheet_name}: {e}")
                continue
        
        return fluids_count
    
    def _extract_fluid_data(self, row: pd.Series, fluid_type: str, equipment_id: int) -> Dict[str, Any]:
        """Extract fluid data for a specific fluid type from a row."""
        fluid_data = {
            'equipment_id': equipment_id,
            'fluid_type': fluid_type,  # Always use the exact fluid type from fluid_types list
            'accounting_unit': 'Ltr',
            'capacity_ltrs_kg': 0.0,
            'addl_10_percent_top_up': 0.0,
            'grade': None,
            'periodicity_km': 0,
            'periodicity_hrs': 0,
            'periodicity_months': 0,
            'date_of_change': None
        }
        
        logger.debug(f"Extracting fluid data for: {fluid_type} (equipment ID: {equipment_id})")
        
        # Find columns related to this fluid type with better matching
        matching_columns = []
        fluid_type_upper = fluid_type.upper()
        
        # Define separators used in Excel column headers
        separators = [' -> ', ' : ', ' - ', ' >', ':', '>', '-', '.', ' ']
        
        # First pass - identify all columns that might be related to this fluid
        for col in row.index:
            col_str = str(col).upper()
            
            # Method 1: Exact match for the fluid type
            if col_str == fluid_type_upper:
                matching_columns.append((col, None, 'exact_match'))
                continue
                
            # Method 2: Fluid type followed by separator and attribute
            for separator in separators:
                if separator in col_str:
                    parts = col_str.split(separator, 1)
                    if len(parts) == 2 and parts[0].strip() == fluid_type_upper:
                        matching_columns.append((col, parts[1].strip(), 'separator_match'))
                        break
        
        # Log found columns
        if matching_columns:
            logger.debug(f"Found {len(matching_columns)} matching columns for {fluid_type}")
        else:
            logger.debug(f"No matching columns found for fluid type: {fluid_type}")
            return None  # No columns found for this fluid type
        
        # Process all matched columns to extract the data
        for col, attribute, match_type in matching_columns:
            col_str = str(col).upper()
            value = row[col]
            
            # Skip empty/null values
            if pd.isna(value):
                continue
                
            # Extract different attribute types based on column header
            if 'CAPACITY' in col_str and 'LTRS/KG' in col_str:
                fluid_data['capacity_ltrs_kg'] = self._parse_numeric(value)
                logger.debug(f"Extracted capacity: {fluid_data['capacity_ltrs_kg']} from {col}")
                
            elif 'ADDL 10% TOP UP' in col_str or 'TOP UP' in col_str:
                fluid_data['addl_10_percent_top_up'] = self._parse_numeric(value)
                logger.debug(f"Extracted top-up: {fluid_data['addl_10_percent_top_up']} from {col}")
                
            elif 'GRADE' in col_str:
                fluid_data['grade'] = str(value) if pd.notna(value) else None
                logger.debug(f"Extracted grade: {fluid_data['grade']} from {col}")
                
            elif any(term in col_str for term in ['PERIODICITY', 'FREQUENCY', 'INTERVAL']):
                if any(km_term in col_str for km_term in ['KM', 'KILOMETER']):
                    fluid_data['periodicity_km'] = self._parse_numeric(value)
                    logger.debug(f"Extracted periodicity_km: {fluid_data['periodicity_km']} from {col}")
                    
                elif any(hr_term in col_str for hr_term in ['HRS', 'HOUR', 'HRS/MONTH']):
                    fluid_data['periodicity_hrs'] = self._parse_numeric(value)
                    logger.debug(f"Extracted periodicity_hrs: {fluid_data['periodicity_hrs']} from {col}")
                    
                elif any(month_term in col_str for month_term in ['MONTH', 'MONTHLY']):
                    fluid_data['periodicity_months'] = self._parse_numeric(value)
                    logger.debug(f"Extracted periodicity_months: {fluid_data['periodicity_months']} from {col}")
                    
            elif any(dt_term in col_str for dt_term in ['DT OF CHANGE', 'DATE', 'CHANGE DATE', 'LAST CHANGE', 'DT OF', 'OF CHANGE']):
                date_str = self._parse_date(value)
                fluid_data['date_of_change'] = date_str
                logger.debug(f"Extracted date_of_change: '{date_str}' from '{col}' with value '{value}'")
        
        return fluid_data if fluid_data['capacity_ltrs_kg'] > 0 else None
    
    def _find_equipment_id(self, equipment_data: Dict[str, Any]) -> Optional[int]:
        """Find equipment ID by matching equipment data."""
        import sqlite3
        import config
        
        try:
            conn = sqlite3.connect(config.DB_PATH)
            cursor = conn.cursor()
            
            # Try to find by BA number first (most specific)
            if equipment_data.get('ba_number'):
                cursor.execute('SELECT equipment_id FROM equipment WHERE ba_number = ?', 
                             (equipment_data['ba_number'],))
                result = cursor.fetchone()
                if result:
                    conn.close()
                    return result[0]
            
            # Try to find by make_and_type and serial_number
            if equipment_data.get('make_and_type') and equipment_data.get('serial_number'):
                cursor.execute('SELECT equipment_id FROM equipment WHERE make_and_type = ? AND serial_number = ?', 
                             (equipment_data['make_and_type'], equipment_data['serial_number']))
                result = cursor.fetchone()
                if result:
                    conn.close()
                    return result[0]
            
            # Try to find by make_and_type only (less specific)
            if equipment_data.get('make_and_type'):
                cursor.execute('SELECT equipment_id FROM equipment WHERE make_and_type = ? ORDER BY equipment_id LIMIT 1', 
                             (equipment_data['make_and_type'],))
                result = cursor.fetchone()
                if result:
                    conn.close()
                    return result[0]
            
            conn.close()
            return None
            
        except Exception as e:
            logger.error(f"Error finding equipment ID: {e}")
            return None
    
    def _save_fluid_to_db(self, fluid_data: Dict[str, Any]) -> bool:
        """Save fluid data to database."""
        import sqlite3
        import config
        
        logger.debug(f"Saving fluid data: {fluid_data}")
        
        # Only proceed if we have a valid fluid type and capacity
        if not fluid_data.get('fluid_type') or not fluid_data.get('capacity_ltrs_kg'):
            logger.warning(f"Skipping fluid save - missing fluid_type or capacity")
            return False
        
        # Define canonical fluid types - only these will be accepted
        canonical_fluid_types = ['ENG OIL', 'HYDRAULIC FLUID', 'COOLANT', 'GREASE ENGINE CLUTCH', 
                      'ROAD WHEELS ARMS', 'BRAKE FLUID', 'TRANSMISSION OIL', 'GEAR BOX', 'GEAR BOX (TXN OIL)',
                      'REAR AXLE', 'FRONT AXLE', 'HUB GREASE', 'NIPPLE GREASE', 'STEERING OIL', 
                      'ROAD WHEELS ARMS IDLER WHEEL & TRACK ADJUSTING MECHANISM GREASE',
                      'GREASE ENGINE CLUTCH RELEASE MECHANISM', 'CLUTCH', 'GREASE', 'CLUTCH OIL', 
                      'BRAKE & CLUTCH', 'HYD OIL', 'HUB OIL', 'DIFFERENTIAL', 'TXN OIL', 'TRANSMISSION SYS']
        
        # Validate fluid type is canonical
        if fluid_data['fluid_type'] not in canonical_fluid_types:
            logger.warning(f"Skipping non-canonical fluid type: {fluid_data['fluid_type']}")
            return False
        
        try:
            conn = sqlite3.connect(config.DB_PATH)
            cursor = conn.cursor()
            
            # Use UPPER() for case-insensitive matching to find and prevent duplicates
            cursor.execute('''
                SELECT fluid_id FROM fluids 
                WHERE equipment_id = ? AND UPPER(fluid_type) = UPPER(?)
            ''', (fluid_data['equipment_id'], fluid_data['fluid_type']))
            
            existing = cursor.fetchone()
            
            if existing:
                # Update existing fluid
                cursor.execute('''
                    UPDATE fluids SET
                        accounting_unit = ?,
                        capacity_ltrs_kg = ?,
                        addl_10_percent_top_up = ?,
                        grade = ?,
                        periodicity_km = ?,
                        periodicity_hrs = ?,
                        periodicity_months = ?,
                        date_of_change = ?
                    WHERE fluid_id = ?
                ''', (
                    fluid_data['accounting_unit'],
                    fluid_data['capacity_ltrs_kg'],
                    fluid_data['addl_10_percent_top_up'],
                    fluid_data['grade'],
                    fluid_data['periodicity_km'],
                    fluid_data['periodicity_hrs'],
                    fluid_data['periodicity_months'],
                    fluid_data['date_of_change'],
                    existing[0]
                ))
            else:
                # Insert new fluid
                cursor.execute('''
                    INSERT INTO fluids (
                        equipment_id, fluid_type, accounting_unit,
                        capacity_ltrs_kg, addl_10_percent_top_up, grade,
                        periodicity_km, periodicity_hrs, periodicity_months,
                        date_of_change
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    fluid_data['equipment_id'],
                    fluid_data['fluid_type'],
                    fluid_data['accounting_unit'],
                    fluid_data['capacity_ltrs_kg'],
                    fluid_data['addl_10_percent_top_up'],
                    fluid_data['grade'],
                    fluid_data['periodicity_km'],
                    fluid_data['periodicity_hrs'],
                    fluid_data['periodicity_months'],
                    fluid_data['date_of_change']
                ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"Error saving fluid to database: {e}")
            return False
    
    def _extract_and_save_maintenance(self, df: pd.DataFrame, sheet_name: str) -> int:
        """Extract maintenance data from DataFrame and save to database."""
        maintenance_count = 0
        
        # Find equipment-related columns
        equipment_col_map = self._map_equipment_columns(df.columns)
        
        for index, row in df.iterrows():
            try:
                # Get equipment data to link maintenance to equipment
                equipment_data = self._extract_equipment_data(row, equipment_col_map, sheet_name)
                
                if not equipment_data or not equipment_data.get('make_and_type') or not self._is_valid_equipment_record(equipment_data):
                    continue
                
                # Find the equipment ID
                equipment_id = self._find_equipment_id(equipment_data)
                if not equipment_id:
                    continue
                
                # Extract maintenance data for TM-1 and TM-2 (match UI categories)
                maintenance_types = [
                    ('TM-1', 'TM - I'),
                    ('TM-2', 'TM - II')
                ]
                
                for category, pattern in maintenance_types:
                    maintenance_data = self._extract_maintenance_data(row, pattern, category, equipment_id)
                    
                    if maintenance_data:
                        # Save maintenance data to database
                        if self._save_maintenance_to_db(maintenance_data):
                            maintenance_count += 1
                            logger.info(f"Saved maintenance: {category} for equipment ID {equipment_id}")
                
            except Exception as e:
                logger.error(f"Error processing maintenance for row {index} in {sheet_name}: {e}")
                continue
        
        return maintenance_count
    
    def _extract_maintenance_data(self, row: pd.Series, pattern: str, category: str, equipment_id: int) -> Optional[Dict[str, Any]]:
        """Extract maintenance data for a specific maintenance type from a row."""
        maintenance_data = {
            'equipment_id': equipment_id,
            'maintenance_category': category,
            'maintenance_type': f"{category} Maintenance",
            'done_date': None,
            'due_date': None,
            'status': 'scheduled',
            'completion_notes': f'Imported from Excel sheet: {pattern}'
        }
        
        # Find columns for this maintenance type
        done_date = None
        due_date = None
        column_exists = False  # Flag to track if any columns for this maintenance type exist
        
        # REMOVED: Check for fluid-based maintenance patterns first
        # This was incorrectly creating maintenance records from fluid data
        # Fluids should be handled by _extract_and_save_fluids() method only
        
        for col in row.index:
            col_str = str(col).upper()
            
            # Define possible variations of TM-I and TM-II patterns
            tm1_patterns = ['TM-I', 'TM - I', 'TM1', 'TM 1', 'TECHNICAL MAINTENANCE 1']
            tm2_patterns = ['TM-II', 'TM - II', 'TM2', 'TM 2', 'TECHNICAL MAINTENANCE 2']
            
            # Check for pattern match based on maintenance category
            pattern_list = tm1_patterns if category == 'TM-1' else tm2_patterns
            
            if any(p in col_str for p in pattern_list) or pattern in col_str:
                column_exists = True  # Mark that at least one column for this maintenance type exists
                logger.info(f"Found {category} column for equipment ID {equipment_id}: {col}")
                
                # Extract dates if available
                if 'DONE' in col_str:
                    done_date = self._parse_date(row[col])
                    if done_date:
                        logger.info(f"Found {category} DONE date: {done_date} from column {col}")
                elif 'DUE' in col_str:
                    due_date = self._parse_date(row[col])
                    if due_date:
                        logger.info(f"Found {category} DUE date: {due_date} from column {col}")
        
        # Set dates and status
        if done_date:
            maintenance_data['done_date'] = done_date
            # Keep status as 'scheduled' for imported records - only UI Complete button should set 'completed'
            # This allows imported records to show time-based status for their next maintenance cycle
        
        if due_date:
            maintenance_data['due_date'] = due_date
        
        # Return maintenance data even if no dates are present, as long as the column exists
        if column_exists:
            logger.info(f"Returning maintenance data for {category} with done_date={done_date}, due_date={due_date}")
            return maintenance_data
        else:
            return None
    
    # REMOVED: _extract_fluid_maintenance_data method
    # This method was incorrectly creating maintenance records from fluid data
    # Fluids should be handled separately by _extract_and_save_fluids() method
    
    # REMOVED: _extract_periodicity_months and _calculate_next_due_from_done_date methods
    # These were only used by the removed _extract_fluid_maintenance_data method
    
    def _save_maintenance_to_db(self, maintenance_data: Dict[str, Any]) -> bool:
        """Save maintenance data to database."""
        import sqlite3
        import config
        
        try:
            conn = sqlite3.connect(config.DB_PATH)
            cursor = conn.cursor()
            
            # Check if maintenance already exists for this equipment and category
            cursor.execute('''
                SELECT maintenance_id FROM maintenance 
                WHERE equipment_id = ? AND maintenance_category = ?
                ORDER BY maintenance_id DESC LIMIT 1
            ''', (maintenance_data['equipment_id'], maintenance_data['maintenance_category']))
            
            existing = cursor.fetchone()
            
            if existing:
                # Update existing maintenance - only set done_date, let system calculate next_due_date
                cursor.execute('''
                    UPDATE maintenance SET
                        maintenance_type = ?,
                        done_date = ?,
                        status = ?,
                        completion_notes = ?
                    WHERE maintenance_id = ?
                ''', (
                    maintenance_data['maintenance_type'],
                    maintenance_data['done_date'],
                    maintenance_data['status'],
                    maintenance_data['completion_notes'],
                    existing[0]
                ))
            else:
                # Insert new maintenance - only set done_date, let system calculate next_due_date
                cursor.execute('''
                    INSERT INTO maintenance (
                        equipment_id, maintenance_type, maintenance_category,
                        done_date, status, completion_notes
                    ) VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    maintenance_data['equipment_id'],
                    maintenance_data['maintenance_type'],
                    maintenance_data['maintenance_category'],
                    maintenance_data['done_date'],
                    maintenance_data['status'],
                    maintenance_data['completion_notes']
                ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"Error saving maintenance to database: {e}")
            return False
    
    def _parse_numeric(self, value: Any) -> float:
        """Parse numeric value with error handling."""
        if pd.isna(value):
            return 0.0
        
        try:
            # Handle string values
            if isinstance(value, str):
                # Remove common non-numeric characters
                cleaned = re.sub(r'[^\d.-]', '', value.replace(',', ''))
                return float(cleaned) if cleaned else 0.0
            return float(value)
        except (ValueError, TypeError):
            return 0.0
    
    def _parse_date(self, value: Any) -> Optional[str]:
        """Parse date value from various formats to ISO format."""
        import re
        import xlrd
        from datetime import datetime, date
        
        # Return None for missing values or just dashes
        if pd.isna(value) or value is None or str(value).strip() == '' or str(value).strip() == '-':
            logger.debug(f"Skipping empty or dash-only date value: '{value}'")
            return None
        
        try:
            # Convert to string for consistent processing
            if isinstance(value, (int, float)):
                # May be Excel's numeric date format
                # Try multiple Excel date origins and workday options
                for date_mode in [0, 1]:  # 0 = 1900-based, 1 = 1904-based
                    try:
                        date_value = datetime(*xlrd.xldate_as_tuple(value, date_mode))
                        result = date_value.strftime('%Y-%m-%d')
                        logger.info(f"Successfully parsed Excel numeric date {value} as {result} using mode {date_mode}")
                        return result
                    except Exception as e:
                        pass
                        
                # Try pandas datetime conversion methods
                origins = ['1899-12-30', '1904-01-01']
                for origin in origins:
                    try:
                        result = pd.to_datetime(value, unit='D', origin=origin).strftime('%Y-%m-%d')
                        logger.info(f"Successfully parsed Excel numeric date {value} as {result} using pandas with origin {origin}")
                        return result
                    except Exception as e:
                        pass
                        
                # If direct conversion fails, try timestamp handling
                try:
                    # Handle possible Excel timestamp
                    if value > 10000:  # Possible Unix timestamp
                        ts = datetime.fromtimestamp(value)
                        return ts.strftime('%Y-%m-%d')
                except Exception:
                    pass
            
            # Handle pandas and datetime objects
            if isinstance(value, (pd.Timestamp, datetime, date)):
                if isinstance(value, pd.Timestamp):
                    return value.strftime('%Y-%m-%d')
                elif isinstance(value, datetime):
                    return value.date().isoformat()
                elif isinstance(value, date):
                    return value.isoformat()
            
            # Handle string dates
            if isinstance(value, str):
                # Clean the string
                value = value.strip()
                
                # Skip if it's just a dash or other non-date placeholder
                if value == '-' or value == 'NA' or value == 'N/A' or len(value) < 2:
                    logger.debug(f"Skipping invalid date format: '{value}'")
                    return None
                    
                # Try common date formats - adding more formats
                formats = [
                    '%d/%m/%Y', '%d-%m-%Y', '%Y-%m-%d', '%d.%m.%Y', '%d/%m/%y', 
                    '%d-%b-%Y', '%d-%b-%y', '%d%m%Y', '%Y%m%d', '%m/%d/%Y', '%m-%d-%Y',
                    '%d %b %Y', '%d %b %y', '%d-%B-%Y', '%d-%B-%y', '%d %B %Y', '%d %B %y',
                    '%b %d, %Y', '%B %d, %Y', '%d.%b.%Y', '%d.%b.%y',
                    '%d/%b/%Y', '%d/%b/%y', '%b/%d/%Y', '%b/%d/%y',
                    '%Y.%m.%d', '%y.%m.%d'
                ]
                
                for fmt in formats:
                    try:
                        parsed_date = datetime.strptime(value, fmt)
                        return parsed_date.date().isoformat()
                    except ValueError:
                        continue
                
                # Special case: Try to handle custom formats like "29-Aug-95"
                try:
                    # Common month abbreviations (both 3-letter and full)
                    month_abbrs = {
                        'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
                        'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12,
                        'January': 1, 'February': 2, 'March': 3, 'April': 4, 'May': 5, 'June': 6,
                        'July': 7, 'August': 8, 'September': 9, 'October': 10, 'November': 11, 'December': 12
                    }
                    
                    # Direct pattern match for "DD-MMM-YY" format
                    pattern = r'(\d{1,2})[-\\/](\w{3,9})[-\\/](\d{2,4})'
                    match = re.match(pattern, str(value))
                    if match:
                        day = int(match.group(1))
                        month_str = match.group(2).capitalize()
                        year_str = match.group(3)
                        
                        # Find month number
                        month = None
                        for m_name, m_num in month_abbrs.items():
                            if month_str.startswith(m_name) or m_name.startswith(month_str):
                                month = m_num
                                break
                                
                        if month:
                            # Handle 2-digit years
                            if len(year_str) <= 2:
                                year = int(year_str)
                                # If year is 90-99, assume 1990s
                                if year >= 90:
                                    year += 1900
                                else:
                                    year += 2000
                            else:
                                year = int(year_str)
                            
                            logger.info(f"Parsed date '{value}' as {year}-{month:02d}-{day:02d}")
                            return f"{year:04d}-{month:02d}-{day:02d}"
                            
                    # If direct match failed, try with split (handles more general cases)
                    parts = re.split(r'[-\\/]', str(value))
                    if len(parts) == 3:
                        # Identify parts
                        day, month, year = None, None, None
                        
                        for i, part in enumerate(parts):
                            parts[i] = part.strip()
                            
                        # Check if any part is a month name
                        for i, part in enumerate(parts):
                            for m_name, m_num in month_abbrs.items():
                                if m_name.lower() in part.lower() or part.lower() in m_name.lower():
                                    month = m_num
                                    month_idx = i
                                    break
                            if month:
                                break
                                
                        if month:
                            # Remove month part and identify day and year
                            day_year_parts = [p for i, p in enumerate(parts) if i != month_idx]
                            
                            if len(day_year_parts) == 2:
                                try:
                                    # Determine which part is day vs year
                                    if len(day_year_parts[0]) <= 2 and int(day_year_parts[0]) <= 31:
                                        day = int(day_year_parts[0])
                                        year_part = day_year_parts[1]
                                    elif len(day_year_parts[1]) <= 2 and int(day_year_parts[1]) <= 31:
                                        day = int(day_year_parts[1])
                                        year_part = day_year_parts[0]
                                    else:  # Assume first is day, second is year
                                        day = int(day_year_parts[0])
                                        year_part = day_year_parts[1]
                                    
                                    # Handle 2-digit years
                                    if len(year_part) <= 2:
                                        year = int(year_part)
                                        # If year is 90-99, assume 1990s
                                        if year >= 90:
                                            year += 1900
                                        else:
                                            year += 2000
                                    else:
                                        year = int(year_part)
                                    
                                    logger.info(f"Parsed date '{value}' as {year}-{month:02d}-{day:02d}")
                                    return f"{year:04d}-{month:02d}-{day:02d}"
                                except:
                                    pass
                except:
                    pass
            
            # Try pandas parsing with different formats as last resort
            if pd.notna(value) and value != '-' and str(value).strip() != '':
                # Try multiple pandas parsing approaches
                parse_methods = ['infer', 'ISO8601', 'mixed']
                for method in parse_methods:
                    try:
                        parsed = pd.to_datetime(value, dayfirst=True, format=None, errors='raise')
                        result = parsed.strftime('%Y-%m-%d')
                        logger.info(f"Successfully parsed date {value} as {result} using pandas with dayfirst=True")
                        return result
                    except Exception:
                        try:
                            parsed = pd.to_datetime(value, dayfirst=False, format=None, errors='raise')
                            result = parsed.strftime('%Y-%m-%d')
                            logger.info(f"Successfully parsed date {value} as {result} using pandas with dayfirst=False")
                            return result
                        except Exception:
                            pass
                
                # Last attempt - try a very permissive dateutil parser directly
                try:
                    from dateutil import parser as date_parser
                    parsed = date_parser.parse(str(value), fuzzy=True)
                    result = parsed.strftime('%Y-%m-%d')
                    logger.info(f"Successfully parsed date {value} as {result} using dateutil parser")
                    return result
                except Exception:
                    pass
                
            # Log original value for debugging
            logger.warning(f"Could not parse date value: {value} of type {type(value)}")
            if pd.isna(value) or value == '-' or str(value).strip() == '':
                return None
            return str(value)  # Return the original value as string if can't parse
            
        except Exception as e:
            logger.error(f"Error parsing date: {e} for value {value}")
            return str(value)  # Return the original value as string

    def _extract_and_save_overhauls(self, df: pd.DataFrame, sheet_name: str) -> int:
        """Extract overhaul data from DataFrame and save to database."""
        overhaul_count = 0
        
        # Find equipment-related columns
        equipment_col_map = self._map_equipment_columns(df.columns)
        
        # First pass: identify equipment with OH-I and OH-II columns, regardless of DONE dates
        equipment_with_oh_types = {}
        release_dates = {}
        
        for index, row in df.iterrows():
            try:
                # Get equipment data to link overhaul to equipment
                equipment_data = self._extract_equipment_data(row, equipment_col_map, sheet_name)
                
                if not equipment_data or not equipment_data.get('make_and_type') or not self._is_valid_equipment_record(equipment_data):
                    continue
                
                # Find the equipment ID
                equipment_id = self._find_equipment_id(equipment_data)
                if not equipment_id:
                    continue
                
                # Add this equipment to the tracking dictionary
                if equipment_id not in equipment_with_oh_types:
                    equipment_with_oh_types[equipment_id] = set()
                    
                # Check for OH-I and OH-II columns and release date
                for col in row.index:
                    col_str = str(col).upper()
                    
                    # Extract release date using patterns mentioned by the user with more flexible matching
                    rel_date_patterns = [
                        'DATE OF REL', 'DATE OF COMMISSION', 'DATE OF INDUCTION',
                        'RELEASE DATE', 'COMMISSION DATE', 'COMMISSIONING DATE', 'REL DATE',
                        'DATE OF COM', 'DATE REL', 'INDUCTION DATE', 'INDUCT DATE'
                    ]
                    
                    # Check for any of the patterns in the column
                    if any(rel_term in col_str for rel_term in rel_date_patterns) or 'REL' in col_str or 'COMMISSION' in col_str or 'INDUCTION' in col_str:
                        rel_date = self._parse_date(row[col])
                        if rel_date:
                            release_dates[equipment_id] = rel_date
                            logger.info(f"Found release date: {rel_date} for equipment ID {equipment_id} from column {col}")
                            logger.info(f"Original value: {row[col]} from column: {col_str}")
                    
                    # Define OH-I and OH-II patterns for column detection - excluding BOH and MLOH as requested
                    oh1_patterns = ['OH-I', 'OH1', 'OH 1', 'OH I', '1ST OH', '1ST OVERHAUL']
                    oh2_patterns = ['OH-II', 'OH2', 'OH 2', 'OH II', '2ND OH', '2ND OVERHAUL']
                    
                    # Check for OH-I columns, regardless of whether they have DONE dates
                    if any(pattern in col_str for pattern in oh1_patterns):
                        equipment_with_oh_types[equipment_id].add('OH-I')
                        logger.info(f"Found OH-I column for equipment ID {equipment_id}: {col}")
                        
                        # If it's also a DONE column, also log the done date if it exists
                        if 'DONE' in col_str:
                            done_date = self._parse_date(row[col])
                            if done_date and str(done_date).strip() and str(done_date).strip() not in ['-', 'None', 'nan']:
                                logger.info(f"Found OH-I DONE date: {done_date} for equipment ID {equipment_id} from column {col}")
                    
                    # Check for OH-II columns, regardless of whether they have DONE dates
                    elif any(pattern in col_str for pattern in oh2_patterns):
                        equipment_with_oh_types[equipment_id].add('OH-II')
                        logger.info(f"Found OH-II column for equipment ID {equipment_id}: {col}")
                        
                        # If it's also a DONE column, also log the done date if it exists
                        if 'DONE' in col_str:
                            done_date = self._parse_date(row[col])
                            if done_date and str(done_date).strip() and str(done_date).strip() not in ['-', 'None', 'nan']:
                                logger.info(f"Found OH-II DONE date: {done_date} for equipment ID {equipment_id} from column {col}")
            except Exception as e:
                logger.error(f"Error in first pass overhaul extraction for row {index} in {sheet_name}: {e}")
                continue
                
        # Second pass: process and save equipment with OH columns, even if done dates are empty
        for index, row in df.iterrows():
            try:
                # Get equipment data to link overhaul to equipment
                equipment_data = self._extract_equipment_data(row, equipment_col_map, sheet_name)
                
                if not equipment_data or not equipment_data.get('make_and_type') or not self._is_valid_equipment_record(equipment_data):
                    continue
                
                # Find the equipment ID
                equipment_id = self._find_equipment_id(equipment_data)
                if not equipment_id:
                    continue
                    
                # Include ALL equipment for overhaul processing - we won't filter
                # This will ensure every piece of equipment shows up in the UI
                if equipment_id not in equipment_with_oh_types:
                    equipment_with_oh_types[equipment_id] = set()
                    logger.debug(f"Adding equipment ID {equipment_id} with no OH data")
                
                logger.info(f"Processing equipment ID {equipment_id} with OH types: {equipment_with_oh_types[equipment_id]}")
                    
                # Extract and save only OH-I and OH-II as requested (excluding BOH and MLOH)
                overhaul_types = [
                    ('OH-I', 'OH-I'),
                    ('OH-I', 'OH1'),
                    ('OH-II', 'OH-II'),
                    ('OH-II', 'OH2')
                ]
                
                # Add release date if available
                if equipment_id in release_dates:
                    self._save_release_date(equipment_id, release_dates[equipment_id])
                
                for overhaul_type, pattern in overhaul_types:
                    # Extract overhaul data, including equipment with OH columns but no done dates
                    overhaul_data = self._extract_overhaul_data(row, pattern, overhaul_type, equipment_id, done_only=False)
                    
                    if overhaul_data:
                        # Save overhaul data to database
                        if self._save_overhaul_to_db(overhaul_data):
                            overhaul_count += 1
                            logger.info(f"Saved overhaul: {overhaul_type} for equipment ID {equipment_id}")
                
            except Exception as e:
                logger.error(f"Error processing overhaul for row {index} in {sheet_name}: {e}")
                continue
        
        return overhaul_count
    
    def _save_release_date(self, equipment_id: int, release_date: str) -> bool:
        """Save equipment release date to database."""
        import sqlite3
        import config
        
        try:
            conn = sqlite3.connect(config.DB_PATH)
            cursor = conn.cursor()
            
            # First ensure the release_date column exists in the equipment table
            try:
                cursor.execute("PRAGMA table_info(equipment)")
                columns = [col[1] for col in cursor.fetchall()]
                
                # If release_date column doesn't exist, add it
                if 'release_date' not in columns:
                    logger.info("Adding release_date column to equipment table")
                    cursor.execute("ALTER TABLE equipment ADD COLUMN release_date TEXT")
                if 'date_of_commission' not in columns:
                    logger.info("Adding date_of_commission column to equipment table")
                    cursor.execute("ALTER TABLE equipment ADD COLUMN date_of_commission TEXT")
            except Exception as e:
                logger.error(f"Error checking/adding release_date column: {e}")
            
            # Update both release_date and date_of_commission (for compatibility)
            cursor.execute("""
                UPDATE equipment 
                SET release_date = ?, date_of_commission = ? 
                WHERE equipment_id = ?
            """, (release_date, release_date, equipment_id))
            
            conn.commit()
            conn.close()
            logger.info(f"Saved release date: {release_date} for equipment ID {equipment_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving release date to database: {e}")
            return False
    
    def _extract_overhaul_data(self, row: pd.Series, pattern: str, overhaul_type: str, equipment_id: int, done_only: bool = False) -> Optional[Dict[str, Any]]:
        """Extract overhaul data for a specific overhaul type from a row.
        
        If done_only is True, we're looking only for DONE dates and ignoring DUE dates.
        If pattern doesn't exactly match what's in the Excel, try common variations.
        """
        from datetime import datetime, date
        
        overhaul_data = {
            'equipment_id': equipment_id,
            'overhaul_type': overhaul_type,
            'done_date': None,
            'due_date': None,
            'status': 'scheduled',
            'notes': f'Imported from Excel sheet as {pattern} (mapped to {overhaul_type})',
            'meter_reading': 0,
            'hours_reading': 0
        }
        
        # Find columns for this overhaul type
        done_date = None
        due_date = None
        column_exists = False  # Flag to indicate if the overhaul column exists at all
        
        for col in row.index:
            col_str = str(col).upper()
            
            # Check if this column is related to the overhaul pattern - excluding BOH and MLOH as requested
            oh1_patterns = ['OH-I', 'OH1', 'OH 1', 'OH I', '1ST OH', '1ST OVERHAUL']
            oh2_patterns = ['OH-II', 'OH2', 'OH 2', 'OH II', '2ND OH', '2ND OVERHAUL']
            
            pattern_matched = False
            
            # ONLY match patterns for the specific overhaul type we're looking for
            if overhaul_type == 'OH-I' and any(p in col_str for p in oh1_patterns):
                pattern_matched = True
                column_exists = True  # Marked that the column exists regardless of done date
            elif overhaul_type == 'OH-II' and any(p in col_str for p in oh2_patterns):
                pattern_matched = True
                column_exists = True  # Marked that the column exists regardless of done date
            elif pattern in col_str or pattern.replace('-', '') in col_str or pattern.replace('OH-', 'OH') in col_str:
                pattern_matched = True
                column_exists = True  # Marked that the column exists regardless of done date
                
            if pattern_matched:
                # ONLY check for DONE columns, completely ignore DUE dates as per user's requirements
                if 'DONE' in col_str:
                    done_date = self._parse_date(row[col])
                    if done_date:
                        overhaul_data['done_date'] = done_date
                        logger.info(f"Found {overhaul_type} DONE date: {done_date} from column {col}")
                # We're intentionally not processing DUE dates per user's request
        
        # Set dates first
        if done_date:
            overhaul_data['done_date'] = done_date
        
        if due_date:
            overhaul_data['due_date'] = due_date
        
        # Use centralized overhaul status calculation instead of simplified logic
        try:
            import overhaul_service

            # Get equipment data for status calculation
            equipment_data = None
            try:
                from models import Equipment
                equipment_data = Equipment.get_by_id(equipment_id)
            except Exception as e:
                logger.warning(f"Could not get equipment data for status calculation: {e}")

            # Extract required data for status calculation
            date_of_commission = equipment_data.get('date_of_commission') if equipment_data else None
            meterage_km = equipment_data.get('meterage_kms') or equipment_data.get('MeterageKMs') if equipment_data else None

            # Get OH-I done date for OH-II calculations
            oh1_done_date = None
            if overhaul_type == 'OH-II' and equipment_data:
                try:
                    from models import Overhaul
                    oh1_overhauls = [oh for oh in Overhaul.get_by_equipment(equipment_id) if oh.get('overhaul_type') == 'OH-I']
                    if oh1_overhauls:
                        oh1_done_date = oh1_overhauls[0].get('done_date')
                except Exception as e:
                    logger.warning(f"Could not get OH-I done date for OH-II calculation: {e}")

            # Calculate status using centralized function
            calculated_status = overhaul_service.get_overhaul_status(
                overhaul_type,
                due_date,
                done_date,
                date_of_commission=date_of_commission,
                oh1_done_date=oh1_done_date,
                meterage_km=meterage_km
            )

            overhaul_data['status'] = calculated_status
            logger.info(f"Calculated overhaul status for {overhaul_type}: {calculated_status}")

        except Exception as e:
            logger.error(f"Error calculating overhaul status using centralized function: {e}")
            # Fallback to basic logic if centralized calculation fails
            if done_date:
                overhaul_data['status'] = 'completed'
            elif due_date:
                overhaul_data['status'] = 'scheduled'
            else:
                overhaul_data['status'] = 'unknown'
        
        # Per user's requirements, include equipment in the overhauls table even if done dates are empty, 
        # as long as the OH-I and OH-II columns exist
        logger.info(f"Overhaul extraction for {overhaul_type}: done_date={done_date}, column_exists={column_exists}")
        
        if done_date:
            # If we have a done date, return the data with that done date
            logger.info(f"Returning overhaul data with DONE date: {done_date} for {overhaul_type}")
            # Make sure due_date is None to ensure we don't show any DUE dates in the UI for existing done dates
            overhaul_data['due_date'] = None
            return overhaul_data
        elif column_exists and not done_only:
            # If column exists but no done date and we're not restricted to done_only, return the data anyway
            # This ensures equipment with OH-I and OH-II columns appear in the overhauls table even without done dates
            logger.info(f"Returning overhaul data without DONE date for {overhaul_type} as column exists")
            return overhaul_data
            
        # Return None if there's no column or if done_only is True and we don't have a done date
        return None
    
    def _save_overhaul_to_db(self, overhaul_data: Dict[str, Any]) -> bool:
        """Save overhaul data to database."""
        import sqlite3
        import config
        from datetime import datetime, timedelta
        
        try:
            conn = sqlite3.connect(config.DB_PATH)
            cursor = conn.cursor()
            
            # GLOBAL BUSINESS RULE: For OH-I, set due_date to 15 years after release date
            if overhaul_data['overhaul_type'] == 'OH-I':
                # Get the equipment's release date
                cursor.execute('''
                    SELECT release_date FROM equipment WHERE equipment_id = ?
                ''', (overhaul_data['equipment_id'],))
                release_date_row = cursor.fetchone()
                
                if release_date_row and release_date_row[0]:
                    try:
                        # Parse the release date
                        if '-' in release_date_row[0]:
                            # ISO format YYYY-MM-DD
                            release_date = datetime.strptime(release_date_row[0], '%Y-%m-%d')
                        else:
                            # Try common formats
                            formats = ['%d/%m/%Y', '%Y/%m/%d', '%d-%m-%Y', '%Y-%m-%d']
                            for fmt in formats:
                                try:
                                    release_date = datetime.strptime(release_date_row[0], fmt)
                                    break
                                except ValueError:
                                    continue
                        
                        # Calculate due date: 15 years after release
                        due_date = release_date + timedelta(days=15*365)
                        overhaul_data['due_date'] = due_date.strftime('%Y-%m-%d')
                        logger.info(f"Set OH-I due date to 15 years after release: {overhaul_data['due_date']}")
                    except Exception as e:
                        logger.error(f"Error calculating OH-I due date from release date: {e}")
            
            # Check if overhaul already exists for this equipment and type
            cursor.execute('''
                SELECT overhaul_id FROM overhauls 
                WHERE equipment_id = ? AND overhaul_type = ?
                ORDER BY overhaul_id DESC LIMIT 1
            ''', (overhaul_data['equipment_id'], overhaul_data['overhaul_type']))
            
            existing = cursor.fetchone()
            
            if existing:
                # Update existing overhaul
                cursor.execute('''
                    UPDATE overhauls SET
                        done_date = ?,
                        due_date = ?,
                        status = ?,
                        notes = ?,
                        meter_reading = ?,
                        hours_reading = ?
                    WHERE overhaul_id = ?
                ''', (
                    overhaul_data['done_date'],
                    overhaul_data['due_date'],
                    overhaul_data['status'],
                    overhaul_data['notes'],
                    overhaul_data['meter_reading'],
                    overhaul_data['hours_reading'],
                    existing[0]
                ))
            else:
                # Insert new overhaul
                cursor.execute('''
                    INSERT INTO overhauls (
                        equipment_id, overhaul_type, done_date, due_date, 
                        status, notes, meter_reading, hours_reading
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    overhaul_data['equipment_id'],
                    overhaul_data['overhaul_type'],
                    overhaul_data['done_date'],
                    overhaul_data['due_date'],
                    overhaul_data['status'],
                    overhaul_data['notes'],
                    overhaul_data['meter_reading'],
                    overhaul_data['hours_reading']
                ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"Error saving overhaul to database: {e}")
            return False

    def _extract_and_save_conditioning(self, df: pd.DataFrame, sheet_name: str) -> int:
        """Extract conditioning/tyre maintenance data from DataFrame and save to database."""
        conditioning_count = 0
        
        # Find equipment-related columns
        equipment_col_map = self._map_equipment_columns(df.columns)
        
        for index, row in df.iterrows():
            try:
                # Get equipment data to link conditioning to equipment
                equipment_data = self._extract_equipment_data(row, equipment_col_map, sheet_name)
                
                if not equipment_data or not equipment_data.get('make_and_type') or not self._is_valid_equipment_record(equipment_data):
                    continue
                
                # Find equipment ID
                equipment_id = self._find_equipment_id(equipment_data)
                if not equipment_id:
                    logger.warning(f"Could not find equipment for conditioning data: {equipment_data.get('make_and_type')}")
                    continue
                
                # Extract tyre conditioning data
                conditioning_data = self._extract_conditioning_data(row, equipment_id, sheet_name)
                
                if conditioning_data:
                    # Save to database
                    success = self._save_conditioning_to_db(conditioning_data)
                    if success:
                        conditioning_count += 1
                        logger.info(f"Saved conditioning data for equipment ID {equipment_id}")
                    
            except Exception as e:
                logger.error(f"Error processing conditioning data in row {index} of {sheet_name}: {e}")
        
        return conditioning_count

    def _extract_conditioning_data(self, row: pd.Series, equipment_id: int, sheet_name: str) -> Optional[Dict[str, Any]]:
        """Extract conditioning/tyre maintenance data from a row."""
        conditioning_data = {
            'equipment_id': equipment_id,
            'tyre_rotation_kms': 5000,  # Default rotation interval
            'tyre_condition_kms': 0,
            'tyre_condition_years': 0,
            'last_rotation_date': None,
            'date_of_change': None,
            'quantity': 1
        }
        
        has_conditioning_data = False
        
        # Find conditioning-related columns
        for col in row.index:
            col_str = str(col).upper()
            
            # Extract tyre rotation interval
            if 'TYRE' in col_str and 'ROTATION' in col_str:
                rotation_value = self._parse_numeric(row[col])
                if rotation_value and rotation_value > 0:
                    conditioning_data['tyre_rotation_kms'] = int(rotation_value)
                    has_conditioning_data = True
            
            # Extract tyre condition KMS
            elif 'TYRE' in col_str and 'CONDITION' in col_str and 'KM' in col_str:
                condition_value = self._parse_numeric(row[col])
                if condition_value and condition_value > 0:
                    conditioning_data['tyre_condition_kms'] = int(condition_value)
                    has_conditioning_data = True
            
            # Extract tyre condition years (from patterns like "10 YRS", "5 YRS")
            elif 'TYRE' in col_str and 'CONDITION' in col_str and 'YR' in col_str:
                condition_text = str(row[col]) if pd.notna(row[col]) else ""
                # Extract years from text like "45000KM/10 YRS" or "5 YRS"
                import re
                year_match = re.search(r'(\d+)\s*YR', condition_text.upper())
                if year_match:
                    years = int(year_match.group(1))
                    conditioning_data['tyre_condition_years'] = years
                    has_conditioning_data = True
            
            # Extract tyre change date
            elif 'TYRE' in col_str and ('CHANGE' in col_str or 'DT' in col_str):
                change_date = self._parse_date(row[col])
                if change_date:
                    conditioning_data['date_of_change'] = change_date
                    conditioning_data['last_rotation_date'] = change_date  # Use same date for rotation
                    has_conditioning_data = True
        
        # Return conditioning data only if we found some relevant information
        return conditioning_data if has_conditioning_data else None

    def _save_conditioning_to_db(self, conditioning_data: Dict[str, Any]) -> bool:
        """Save conditioning data to the database."""
        try:
            import sqlite3
            import config
            
            conn = sqlite3.connect(config.DB_PATH)
            cursor = conn.cursor()
            
            # Check if conditioning record already exists for this equipment
            cursor.execute('''
                SELECT tyre_maintenance_id 
                FROM tyre_maintenance 
                WHERE equipment_id = ?
            ''', (conditioning_data['equipment_id'],))
            
            existing = cursor.fetchone()
            
            if existing:
                # Update existing record with the latest data from Excel
                cursor.execute('''
                    UPDATE tyre_maintenance SET
                        tyre_rotation_kms = ?,
                        tyre_condition_kms = ?,
                        tyre_condition_years = ?,
                        last_rotation_date = ?,
                        date_of_change = ?,
                        quantity = ?
                    WHERE equipment_id = ?
                ''', (
                    conditioning_data['tyre_rotation_kms'],
                    conditioning_data['tyre_condition_kms'],
                    conditioning_data['tyre_condition_years'],
                    conditioning_data['last_rotation_date'],
                    conditioning_data['date_of_change'],
                    conditioning_data['quantity'],
                    conditioning_data['equipment_id']
                ))
            else:
                # Insert new record
                cursor.execute('''
                    INSERT INTO tyre_maintenance (
                        equipment_id,
                        tyre_rotation_kms,
                        tyre_condition_kms,
                        tyre_condition_years,
                        last_rotation_date,
                        date_of_change,
                        quantity
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    conditioning_data['equipment_id'],
                    conditioning_data['tyre_rotation_kms'],
                    conditioning_data['tyre_condition_kms'],
                    conditioning_data['tyre_condition_years'],
                    conditioning_data['last_rotation_date'],
                    conditioning_data['date_of_change'],
                    conditioning_data['quantity']
                ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"Error saving conditioning data to database: {e}")
            if 'conn' in locals():
                conn.close()
            return False

    def _extract_and_save_batteries(self, df: pd.DataFrame, sheet_name: str) -> int:
        """Extract battery data from DataFrame and save to database."""
        battery_count = 0
        
        # Find equipment-related columns
        equipment_col_map = self._map_equipment_columns(df.columns)
        
        for index, row in df.iterrows():
            try:
                # Get equipment data to link battery to equipment
                equipment_data = self._extract_equipment_data(row, equipment_col_map, sheet_name)
                
                if not equipment_data or not equipment_data.get('make_and_type') or not self._is_valid_equipment_record(equipment_data):
                    continue
                
                # Find equipment ID
                equipment_id = self._find_equipment_id(equipment_data)
                if not equipment_id:
                    continue
                
                # Extract battery data
                battery_data = self._extract_battery_data(row, equipment_id, sheet_name)
                
                if battery_data:
                    # Save battery data to database
                    if self._save_battery_to_db(battery_data):
                        battery_count += 1
                        logger.info(f"Saved battery data for equipment ID {equipment_id}")
                    
            except Exception as e:
                logger.error(f"Error processing battery data in row {index} of {sheet_name}: {e}")
        
        return battery_count

    def _extract_battery_data(self, row: pd.Series, equipment_id: int, sheet_name: str) -> Optional[Dict[str, Any]]:
        """Extract battery data from a row."""
        battery_data = {
            'equipment_id': equipment_id,
            'done_date': None,
            'custom_life_months': 24  # Default 24 months (2 years)
        }
        
        has_battery_data = False
        
        # Find battery-related columns
        for col in row.index:
            col_str = str(col).upper()
            
            # Extract battery change date
            if 'BTY' in col_str and ('CHANGE' in col_str or 'DT' in col_str):
                change_date = self._parse_date(row[col])
                if change_date:
                    battery_data['done_date'] = change_date
                    has_battery_data = True
            
            # Extract battery life (in months or years)
            elif 'BTY' in col_str and 'LIFE' in col_str:
                life_text = str(row[col]) if pd.notna(row[col]) else ""
                # Extract months from text like "24 months", "2 years", "24"
                import re
                
                # Try to find months first
                month_match = re.search(r'(\d+)\s*month', life_text.lower())
                if month_match:
                    months = int(month_match.group(1))
                    battery_data['custom_life_months'] = months
                    has_battery_data = True
                else:
                    # Try to find years and convert to months
                    year_match = re.search(r'(\d+)\s*year', life_text.lower())
                    if year_match:
                        years = int(year_match.group(1))
                        battery_data['custom_life_months'] = years * 12
                        has_battery_data = True
                    else:
                        # Try to extract just a number (assume months)
                        number_match = re.search(r'(\d+)', life_text)
                        if number_match:
                            number = int(number_match.group(1))
                            # If number is reasonable for months (6-60), use as months
                            # If too small (1-5), assume years
                            if 6 <= number <= 60:
                                battery_data['custom_life_months'] = number
                            elif 1 <= number <= 5:
                                battery_data['custom_life_months'] = number * 12
                            else:
                                battery_data['custom_life_months'] = 24  # Default
                            has_battery_data = True
        
        # Return battery data only if we found some relevant information
        return battery_data if has_battery_data else None

    def _save_battery_to_db(self, battery_data: Dict[str, Any]) -> bool:
        """Save battery data to the database."""
        try:
            import sqlite3
            import config
            
            conn = sqlite3.connect(config.DB_PATH)
            cursor = conn.cursor()
            
            # Check if battery record already exists for this equipment
            cursor.execute('''
                SELECT battery_id 
                FROM battery 
                WHERE equipment_id = ?
            ''', (battery_data['equipment_id'],))
            
            existing = cursor.fetchone()
            
            if existing:
                # Update existing record with the latest data from Excel
                cursor.execute('''
                    UPDATE battery SET
                        done_date = ?,
                        custom_life_months = ?
                    WHERE equipment_id = ?
                ''', (
                    battery_data['done_date'],
                    battery_data['custom_life_months'],
                    battery_data['equipment_id']
                ))
            else:
                # Insert new record
                cursor.execute('''
                    INSERT INTO battery (
                        equipment_id,
                        done_date,
                        custom_life_months
                    ) VALUES (?, ?, ?)
                ''', (
                    battery_data['equipment_id'],
                    battery_data['done_date'],
                    battery_data['custom_life_months']
                ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"Error saving battery data to database: {e}")
            if 'conn' in locals():
                conn.close()
            return False


def test_working_import(file_path: str):
    """Test the working importer."""
    print("Testing WORKING Excel Importer that saves real data...")
    
    importer = RobustExcelImporter()
    
    if not importer.initialize_staging():
        print("Failed to initialize database")
        return
    
    success, stats = importer.process_excel_file(file_path)
    print(f"Success: {success}")
    print(f"Stats: {stats}")
    
    # Verify data was actually saved
    try:
        import sqlite3
        import config
        conn = sqlite3.connect(config.DB_PATH)
        cursor = conn.cursor()
        
        # Check equipment
        cursor.execute('SELECT COUNT(*) FROM equipment')
        equipment_count = cursor.fetchone()[0]
        print(f"Equipment records in database: {equipment_count}")
        
        # Check fluids
        cursor.execute('SELECT COUNT(*) FROM fluids')
        fluids_count = cursor.fetchone()[0]
        print(f"Fluids records in database: {fluids_count}")
        
        # Check maintenance
        cursor.execute('SELECT COUNT(*) FROM maintenance')
        maintenance_count = cursor.fetchone()[0]
        print(f"Maintenance records in database: {maintenance_count}")
        
        # Show sample equipment records
        cursor.execute('SELECT equipment_id, make_and_type, ba_number, meterage_kms FROM equipment LIMIT 5')
        records = cursor.fetchall()
        print("\nSample equipment records:")
        for record in records:
            print(f"  ID: {record[0]}, Type: {record[1]}, BA: {record[2]}, KM: {record[3]}")
        
        # Show sample fluids records
        cursor.execute('''
            SELECT f.fluid_id, f.fluid_type, f.capacity_ltrs_kg, f.grade, e.make_and_type
            FROM fluids f 
            JOIN equipment e ON f.equipment_id = e.equipment_id 
            LIMIT 5
        ''')
        fluid_records = cursor.fetchall()
        print("\nSample fluids records:")
        for record in fluid_records:
            print(f"  ID: {record[0]}, Type: {record[1]}, Capacity: {record[2]}, Grade: {record[3]}, Equipment: {record[4]}")
        
        # Show sample maintenance records
        cursor.execute('''
            SELECT m.maintenance_id, m.maintenance_category, m.status, m.done_date, e.make_and_type
            FROM maintenance m 
            JOIN equipment e ON m.equipment_id = e.equipment_id 
            LIMIT 5
        ''')
        maintenance_records = cursor.fetchall()
        print("\nSample maintenance records:")
        for record in maintenance_records:
            print(f"  ID: {record[0]}, Category: {record[1]}, Status: {record[2]}, Done: {record[3]}, Equipment: {record[4]}")
        
        conn.close()
    except Exception as e:
        print(f"Error checking database: {e}")


if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        test_working_import(sys.argv[1])
    else:
        print("Usage: python robust_excel_importer_working.py <file_path>")