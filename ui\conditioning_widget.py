"""Conditioning management widget for the equipment inventory application."""
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                           QPushButton, QTableWidget, QTableWidgetItem,
                           QHeaderView, QAbstractItemView, QMessageBox,
                           QComboBox, QLineEdit, QFormLayout, QGroupBox,
                           QSplitter, QFrame, QDateEdit, QSpinBox)
from PyQt5.QtCore import Qt, QSize, QDate, pyqtSignal
from PyQt5.QtGui import QIcon, QColor

import models
from ui.custom_widgets import ReadOnlyTableWidget, StatusLabel
from ui.dialogs import MaintenanceDialog
# TyreMaintenanceDialog is handled by MaintenanceDialog
import utils
from datetime import date, timedelta
import database
from conditioning_status_service import conditioning_status_service
from conditioning_status_enums import TyreRotationStatus, TyreConditionStatus, StatusColors

class ConditioningWidget(QWidget):
    """Widget for managing conditioning."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """Set up the conditioning widget UI."""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # Title
        title_label = QLabel("Conditioning Management")
        title_label.setObjectName("titleLabel")
        main_layout.addWidget(title_label)
        
        # Create a splitter for the main content
        content_splitter = QSplitter(Qt.Horizontal)
        
        # Left side - Conditioning list
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        
        # Search and filter controls
        filter_layout = QHBoxLayout()
        
        # Search box
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Search equipment...")
        self.search_edit.textChanged.connect(self.filter_conditioning)
        filter_layout.addWidget(self.search_edit)
        
        # Equipment type filter
        self.equipment_type_filter = QComboBox()
        self.equipment_type_filter.addItem("All Types", None)
        filter_layout.addWidget(self.equipment_type_filter)
        
        # Status filter
        self.status_filter = QComboBox()
        self.status_filter.addItems(["All Status", "Due for Rotation", "Due for Inspection", "Normal"])
        self.status_filter.currentIndexChanged.connect(self.filter_conditioning)
        filter_layout.addWidget(self.status_filter)
        
        left_layout.addLayout(filter_layout)
        
        # Conditioning table
        self.conditioning_table = ReadOnlyTableWidget()
        self.conditioning_table.setColumnCount(6)
        self.conditioning_table.setHorizontalHeaderLabels(
            ["Equipment", "Rotation KMs", "Last Rotation", "Current KMs", "Status", "Battery Life"]
        )
        self.conditioning_table.verticalHeader().setVisible(False)
        self.conditioning_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.conditioning_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.conditioning_table.setSortingEnabled(True)
        self.conditioning_table.row_clicked.connect(self.conditioning_selected)
        
        left_layout.addWidget(self.conditioning_table)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.add_button = QPushButton("Add Conditioning")
        self.add_button.clicked.connect(self.add_conditioning)
        button_layout.addWidget(self.add_button)
        
        self.edit_button = QPushButton("Edit Conditioning")
        self.edit_button.clicked.connect(self.edit_conditioning)
        self.edit_button.setEnabled(False)
        button_layout.addWidget(self.edit_button)
        
        self.delete_button = QPushButton("Delete Conditioning")
        self.delete_button.setObjectName("deleteButton")
        self.delete_button.clicked.connect(self.delete_conditioning)
        self.delete_button.setEnabled(False)
        button_layout.addWidget(self.delete_button)
        
        left_layout.addLayout(button_layout)
        
        # Right side - Conditioning details
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        
        # Conditioning details group
        details_group = QGroupBox("Conditioning Details")
        details_layout = QFormLayout(details_group)
        
        self.detail_equipment = QLabel("--")
        details_layout.addRow("Equipment:", self.detail_equipment)
        
        self.detail_rotation_kms = QLabel("--")
        details_layout.addRow("Rotation KMs:", self.detail_rotation_kms)
        
        self.detail_condition_kms = QLabel("--")
        details_layout.addRow("Condition KMs:", self.detail_condition_kms)
        
        self.detail_condition_years = QLabel("--")
        details_layout.addRow("Condition Years:", self.detail_condition_years)
        
        self.detail_last_rotation = QLabel("--")
        details_layout.addRow("Last Rotation Date:", self.detail_last_rotation)
        
        self.detail_battery = QLabel("--")
        details_layout.addRow("Battery Replacement Due:", self.detail_battery)
        
        right_layout.addWidget(details_group)
        
        # Equipment status group
        status_group = QGroupBox("Equipment Status")
        status_layout = QFormLayout(status_group)
        
        self.detail_make_type = QLabel("--")
        status_layout.addRow("Make & Type:", self.detail_make_type)
        
        self.detail_serial = QLabel("--")
        status_layout.addRow("Serial Number:", self.detail_serial)
        
        self.detail_current_kms = QLabel("--")
        status_layout.addRow("Current Meterage:", self.detail_current_kms)
        
        self.detail_kms_since_rotation = QLabel("--")
        status_layout.addRow("KMs Since Last Rotation:", self.detail_kms_since_rotation)
        
        self.detail_rotation_status = StatusLabel("--")
        status_layout.addRow("Rotation Status:", self.detail_rotation_status)
        
        self.detail_condition_status = StatusLabel("--")
        status_layout.addRow("Condition Status:", self.detail_condition_status)
        
        right_layout.addWidget(status_group)
        
        # Quick actions group
        actions_group = QGroupBox("Quick Actions")
        actions_layout = QVBoxLayout(actions_group)
        
        # Record rotation button
        self.record_rotation_button = QPushButton("Record Rotation")
        self.record_rotation_button.clicked.connect(self.record_rotation)
        self.record_rotation_button.setEnabled(False)
        actions_layout.addWidget(self.record_rotation_button)
        
        # Record inspection button
        self.record_inspection_button = QPushButton("Record Inspection")
        self.record_inspection_button.clicked.connect(self.record_inspection)
        self.record_inspection_button.setEnabled(False)
        actions_layout.addWidget(self.record_inspection_button)
        
        right_layout.addWidget(actions_group)
        
        # Conditioning history group
        history_group = QGroupBox("Conditioning History")
        history_layout = QVBoxLayout(history_group)
        
        self.history_table = ReadOnlyTableWidget()
        self.history_table.setColumnCount(3)
        self.history_table.setHorizontalHeaderLabels(
            ["Maintenance Type", "Done Date", "Meterage"]
        )
        history_layout.addWidget(self.history_table)
        
        right_layout.addWidget(history_group)
        
        # Add widgets to splitter
        content_splitter.addWidget(left_widget)
        content_splitter.addWidget(right_widget)
        content_splitter.setSizes([int(self.width() * 0.5), int(self.width() * 0.5)])
        
        main_layout.addWidget(content_splitter)
    
    def load_data(self):
        """Load conditioning data into the table."""
        # Get all conditioning records
        conditioning_list = models.TyreMaintenance.get_all()
        
        # Load equipment type filter
        self.load_equipment_type_filter(conditioning_list)
        
        # Clear table
        self.conditioning_table.setRowCount(0)
        
        # Add data to table
        for row, conditioning in enumerate(conditioning_list):
            self.conditioning_table.insertRow(row)
            
            # Support both camelCase and snake_case keys
            make_type = conditioning.get('make_and_type') or conditioning.get('MakeAndType') or ''
            serial = conditioning.get('serial_number') or conditioning.get('SerialNumber') or ''
            equipment_text = f"{make_type} (SN: {serial})"
            item = QTableWidgetItem(equipment_text)
            item.setData(Qt.UserRole, conditioning.get('tyre_maintenance_id') or conditioning.get('TyreMaintenanceID'))
            # Also store equipment ID and type for filtering
            item.setData(Qt.UserRole + 1, conditioning.get('equipment_id') or conditioning.get('EquipmentID'))
            item.setData(Qt.UserRole + 2, (make_type.split()[0] if make_type else ''))  # First word as type
            self.conditioning_table.setItem(row, 0, item)
            
            # Rotation KMs
            rotation_kms = str(conditioning.get('TyreRotationKMs') or conditioning.get('tyre_rotation_kms') or "--")
            self.conditioning_table.setItem(row, 1, QTableWidgetItem(rotation_kms))
            
            # Last Rotation Date
            last_rotation = conditioning.get('LastRotationDate') or conditioning.get('last_rotation_date') or "--"
            self.conditioning_table.setItem(row, 2, QTableWidgetItem(str(last_rotation)))
            
            # Current KMs
            current_kms_val = conditioning.get('MeterageKMs') or conditioning.get('meterage_kms') or 0
            current_kms = utils.format_decimal(current_kms_val, 3)
            self.conditioning_table.setItem(row, 3, QTableWidgetItem(current_kms))
            
            # Status calculation using centralized service
            current_meterage = float(current_kms_val or 0)
            vintage_years = float(conditioning.get('VintageYears') or conditioning.get('vintage_years') or 0)
            rotation_kms = float(conditioning.get('TyreRotationKMs') or conditioning.get('tyre_rotation_kms') or 0)
            condition_kms = float(conditioning.get('TyreConditionKMs') or conditioning.get('tyre_condition_kms') or 0)
            condition_years = float(conditioning.get('TyreConditionYears') or conditioning.get('tyre_condition_years') or 0)
            last_rotation_date = conditioning.get('last_rotation_date')

            # Get equipment meterage at last rotation from maintenance records
            equipment_meterage_at_last_rotation = None
            conn = database.get_db_connection()
            last_rotation = conn.execute('''
                SELECT meterage_kms AS MeterageKMs FROM maintenance
                WHERE equipment_id = ? AND maintenance_type = 'Tyre Rotation'
                ORDER BY done_date DESC LIMIT 1
            ''', (conditioning.get('equipment_id') or conditioning.get('EquipmentID'),)).fetchone()
            conn.close()

            if last_rotation:
                equipment_meterage_at_last_rotation = float(last_rotation['MeterageKMs'] or 0)

            # Calculate rotation status
            rotation_status = conditioning_status_service.calculate_tyre_rotation_status(
                current_meterage, last_rotation_date, rotation_kms, equipment_meterage_at_last_rotation
            )

            # Calculate condition status
            condition_status = conditioning_status_service.calculate_tyre_condition_status(
                current_meterage, vintage_years, condition_kms, condition_years
            )

            # Determine overall status (most urgent takes precedence)
            if rotation_status.status_level == "critical" or condition_status.status_level == "critical":
                status = str(rotation_status) if rotation_status.status_level == "critical" else str(condition_status)
                status_color = QColor(StatusColors.CRITICAL_COLOR)
            elif rotation_status.status_level == "warning" or condition_status.status_level == "warning":
                status = str(rotation_status) if rotation_status.status_level == "warning" else str(condition_status)
                status_color = QColor(StatusColors.WARNING_COLOR)
            else:
                status = "Normal"
                status_color = QColor(StatusColors.NORMAL_COLOR)
            
            status_item = QTableWidgetItem(status)
            status_item.setForeground(status_color)
            status_item.setData(Qt.UserRole, status)  # Store status for filtering
            self.conditioning_table.setItem(row, 4, status_item)
            
            # Battery Life
            self.conditioning_table.setItem(row, 5, QTableWidgetItem("2 YRS FROM THE DATE OF ISSUE"))
        
        # Resize columns to content
        self.conditioning_table.resizeColumnsToContents()
        
        # Connect filter signals
        self.equipment_type_filter.currentIndexChanged.connect(self.filter_conditioning)
        
        # Clear details
        self.clear_conditioning_details()
    
    def load_equipment_type_filter(self, conditioning_list):
        """Load equipment type filter dropdown."""
        # Clear existing items
        self.equipment_type_filter.clear()
        self.equipment_type_filter.addItem("All Types", None)
        
        # Get unique equipment types
        equipment_types = set()
        for conditioning in conditioning_list:
            make_type = conditioning.get('make_and_type') or conditioning.get('MakeAndType') or ''
            equipment_type = make_type.split()[0] if make_type else ''  # First word as type
            equipment_types.add(equipment_type)
        
        # Add to filter
        for equipment_type in sorted(equipment_types):
            self.equipment_type_filter.addItem(equipment_type, equipment_type)
    
    def filter_conditioning(self):
        """Filter conditioning list based on search text and filters."""
        search_text = self.search_edit.text().lower()
        equipment_type = self.equipment_type_filter.currentData()
        status_index = self.status_filter.currentIndex()
        
        for row in range(self.conditioning_table.rowCount()):
            show_row = True
            
            # Check search text
            if search_text:
                row_text = ""
                for col in range(self.conditioning_table.columnCount()):
                    item = self.conditioning_table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "
                
                if search_text not in row_text:
                    show_row = False
            
            # Check equipment type filter
            if equipment_type is not None:
                item = self.conditioning_table.item(row, 0)
                item_equipment_type = item.data(Qt.UserRole + 2)
                if item_equipment_type != equipment_type:
                    show_row = False
            
            # Check status filter
            if status_index > 0:
                status_item = self.conditioning_table.item(row, 4)
                status = status_item.data(Qt.UserRole)
                
                if status_index == 1 and "Rotation" not in status:  # Due for Rotation
                    show_row = False
                elif status_index == 2 and "Inspection" not in status:  # Due for Inspection
                    show_row = False
                elif status_index == 3 and status != "Normal":  # Normal
                    show_row = False
            
            # Show or hide row
            self.conditioning_table.setRowHidden(row, not show_row)
    
    def conditioning_selected(self, row):
        """Handle conditioning selection."""
        # Enable buttons
        self.edit_button.setEnabled(True)
        self.delete_button.setEnabled(True)
        
        # Get conditioning ID
        conditioning_id = self.conditioning_table.item(row, 0).data(Qt.UserRole)
        
        # Get conditioning details
        conditioning = models.TyreMaintenance.get_by_id(conditioning_id)
        if not conditioning:
            return
        
        # Get equipment details
        equipment = models.Equipment.get_by_id(conditioning.get('equipment_id') or conditioning.get('EquipmentID'))
        if not equipment:
            return
        
        # Update detail labels
        make_type = equipment.get('make_and_type') or equipment.get('MakeAndType') or ''
        serial = equipment.get('serial_number') or equipment.get('SerialNumber') or ''
        self.detail_equipment.setText(f"{make_type} (SN: {serial})")
        self.detail_rotation_kms.setText(str(conditioning.get('TyreRotationKMs') or conditioning.get('tyre_rotation_kms') or "--"))
        self.detail_condition_kms.setText(str(conditioning.get('TyreConditionKMs') or conditioning.get('tyre_condition_kms') or "--"))
        self.detail_condition_years.setText(str(conditioning.get('TyreConditionYears') or conditioning.get('tyre_condition_years') or "--"))
        self.detail_last_rotation.setText(str(conditioning.get('LastRotationDate') or conditioning.get('last_rotation_date') or "--"))
        self.detail_battery.setText("2 YRS FROM THE DATE OF ISSUE")
        
        # Update equipment status
        self.detail_make_type.setText(make_type)
        self.detail_serial.setText(serial)
        self.detail_current_kms.setText(f"{utils.format_decimal(equipment.get('MeterageKMs') or equipment.get('meterage_kms'), 3)} KMs")
        
        # Calculate KMs since last rotation
        current_meterage = float(equipment.get('MeterageKMs') or equipment.get('meterage_kms') or 0)
        
        # Get maintenance records for this equipment to find last rotation meterage
        conn = database.get_db_connection()
        last_rotation = conn.execute('''
            SELECT meterage_kms AS MeterageKMs FROM maintenance 
            WHERE equipment_id = ? AND maintenance_type = 'Tyre Rotation'
            ORDER BY done_date DESC LIMIT 1
        ''', (equipment.get('equipment_id') or equipment.get('EquipmentID'),)).fetchone()
        conn.close()
        
        kms_since_rotation = "--"
        if last_rotation:
            last_rotation_kms = float(last_rotation['MeterageKMs'] or 0)
            kms_since_rotation = utils.format_decimal(current_meterage - last_rotation_kms, 3)
        
        self.detail_kms_since_rotation.setText(f"{kms_since_rotation} KMs")
        
        # Calculate rotation status using centralized service
        rotation_kms = float(conditioning.get('TyreRotationKMs') or conditioning.get('tyre_rotation_kms') or 0)
        last_rotation_date = conditioning.get('last_rotation_date')
        equipment_meterage_at_last_rotation = None

        if last_rotation:
            equipment_meterage_at_last_rotation = float(last_rotation['MeterageKMs'] or 0)

        rotation_status_enum = conditioning_status_service.calculate_tyre_rotation_status(
            current_meterage, last_rotation_date, rotation_kms, equipment_meterage_at_last_rotation
        )

        self.detail_rotation_status.setText(str(rotation_status_enum))
        self.detail_rotation_status.setStatus(rotation_status_enum.status_level)

        # Calculate condition status using centralized service
        condition_kms = float(conditioning.get('TyreConditionKMs') or conditioning.get('tyre_condition_kms') or 0)
        condition_years = float(conditioning.get('TyreConditionYears') or conditioning.get('tyre_condition_years') or 0)
        vintage_years = float(equipment.get('VintageYears') or equipment.get('vintage_years') or 0)

        condition_status_enum = conditioning_status_service.calculate_tyre_condition_status(
            current_meterage, vintage_years, condition_kms, condition_years
        )

        self.detail_condition_status.setText(str(condition_status_enum))
        self.detail_condition_status.setStatus(condition_status_enum.status_level)
        
        # Enable/disable action buttons
        self.record_rotation_button.setEnabled(rotation_status_enum.status_level != "normal")
        self.record_inspection_button.setEnabled(condition_status_enum.status_level != "normal")
        
        # Load conditioning history
        self.load_conditioning_history(equipment.get('equipment_id') or equipment.get('EquipmentID'))
    
    def load_conditioning_history(self, equipment_id):
        """Load conditioning history for the selected equipment."""
        # Get maintenance records for this equipment related to tyres
        conn = database.get_db_connection()
        history = conn.execute('''
            SELECT * FROM maintenance 
            WHERE equipment_id = ? AND 
                  (maintenance_type = 'Tyre Rotation' OR 
                   maintenance_type = 'Tyre Inspection' OR
                   maintenance_type LIKE '%Tyre%')
            ORDER BY done_date DESC
        ''', (equipment_id,)).fetchall()
        conn.close()
        
        # Clear table
        self.history_table.setRowCount(0)
        
        # Add data to table
        for row, maintenance in enumerate(history):
            # Skip if no done date (not completed yet)
            if not maintenance['done_date']:
                continue
                
            self.history_table.insertRow(row)
            
            # Maintenance Type
            item = QTableWidgetItem(maintenance['maintenance_type'])
            item.setData(Qt.UserRole, maintenance['maintenance_id'])
            self.history_table.setItem(row, 0, item)
            
            # Done Date
            self.history_table.setItem(row, 1, QTableWidgetItem(str(maintenance['done_date'])))
            
            # Meterage
            meterage = utils.format_decimal(maintenance['meterage_kms'], 3)
            self.history_table.setItem(row, 2, QTableWidgetItem(meterage))
        
        # Resize columns to content
        self.history_table.resizeColumnsToContents()
    
    def clear_conditioning_details(self):
        """Clear conditioning details."""
        self.detail_equipment.setText("--")
        self.detail_rotation_kms.setText("--")
        self.detail_condition_kms.setText("--")
        self.detail_condition_years.setText("--")
        self.detail_last_rotation.setText("--")
        self.detail_battery.setText("--")
        
        self.detail_make_type.setText("--")
        self.detail_serial.setText("--")
        self.detail_current_kms.setText("--")
        self.detail_kms_since_rotation.setText("--")
        self.detail_rotation_status.setText("--")
        self.detail_rotation_status.setStatus("normal")
        self.detail_condition_status.setText("--")
        self.detail_condition_status.setStatus("normal")
        
        # Clear history table
        self.history_table.setRowCount(0)
        
        # Disable buttons
        self.edit_button.setEnabled(False)
        self.delete_button.setEnabled(False)
        self.record_rotation_button.setEnabled(False)
        self.record_inspection_button.setEnabled(False)
    
    def add_conditioning(self):
        """Add new conditioning record."""
        # Get equipment list for dialog
        equipment_list = models.Equipment.get_active()
        
        dialog = TyreMaintenanceDialog(equipment_list=equipment_list, parent=self)
        if dialog.exec_():
            conditioning_data = dialog.get_tyre_maintenance_data()
            
            # Create conditioning model
            conditioning = models.TyreMaintenance(
                equipment_id=conditioning_data['EquipmentID'],
                tyre_rotation_kms=conditioning_data['TyreRotationKMs'],
                tyre_condition_kms=conditioning_data['TyreConditionKMs'],
                tyre_condition_years=conditioning_data['TyreConditionYears'],
                last_rotation_date=conditioning_data['LastRotationDate']
            )
            
            try:
                # Save to database
                conditioning.save()
                
                # Reload data
                self.load_data()
                
                QMessageBox.information(self, "Success", 
                                      "Conditioning record added successfully.")
            except Exception as e:
                QMessageBox.critical(self, "Error", 
                                   f"Error adding conditioning record: {str(e)}")
    
    def edit_conditioning(self):
        """Edit selected conditioning record."""
        selected_row = self.conditioning_table.currentRow()
        if selected_row < 0:
            return
        
        # Get conditioning ID
        conditioning_id = self.conditioning_table.item(selected_row, 0).data(Qt.UserRole)
        
        # Get conditioning details
        conditioning = models.TyreMaintenance.get_by_id(conditioning_id)
        if not conditioning:
            return
        
        # Get equipment list for dialog
        equipment_list = models.Equipment.get_active()
        
        # Show dialog
        dialog = TyreMaintenanceDialog(conditioning, equipment_list, parent=self)
        if dialog.exec_():
            conditioning_data = dialog.get_tyre_maintenance_data()
            
            # Update conditioning model
            conditioning_model = models.TyreMaintenance(
                tyre_maintenance_id=conditioning_id,
                equipment_id=conditioning_data['EquipmentID'],
                tyre_rotation_kms=conditioning_data['TyreRotationKMs'],
                tyre_condition_kms=conditioning_data['TyreConditionKMs'],
                tyre_condition_years=conditioning_data['TyreConditionYears'],
                last_rotation_date=conditioning_data['LastRotationDate']
            )
            
            try:
                # Save to database
                conditioning_model.save()
                
                # Reload data
                self.load_data()
                
                # Reselect the row
                for row in range(self.conditioning_table.rowCount()):
                    item = self.conditioning_table.item(row, 0)
                    if item and item.data(Qt.UserRole) == conditioning_id:
                        self.conditioning_table.selectRow(row)
                        self.conditioning_selected(row)
                        break
                
                QMessageBox.information(self, "Success", 
                                      "Conditioning record updated successfully.")
            except Exception as e:
                QMessageBox.critical(self, "Error", 
                                   f"Error updating conditioning record: {str(e)}")
    
    def delete_conditioning(self):
        """Delete selected conditioning record."""
        selected_row = self.conditioning_table.currentRow()
        if selected_row < 0:
            return
        
        # Get conditioning ID
        conditioning_id = self.conditioning_table.item(selected_row, 0).data(Qt.UserRole)
        
        # Get conditioning details
        conditioning = models.TyreMaintenance.get_by_id(conditioning_id)
        if not conditioning:
            return
        
        # Get equipment details
        equipment = models.Equipment.get_by_id(conditioning.get('equipment_id') or conditioning.get('EquipmentID'))
        
        # Confirm deletion
        confirm = QMessageBox.question(
            self, "Confirm Deletion", 
            f"Are you sure you want to delete conditioning record for {equipment.get('make_and_type') or equipment.get('MakeAndType')}?",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if confirm != QMessageBox.Yes:
            return
        
        try:
            # Delete from database
            models.TyreMaintenance.delete(conditioning_id)
            
            # Reload data
            self.load_data()
            
            QMessageBox.information(self, "Success", 
                                  "Conditioning record deleted successfully.")
        except Exception as e:
            QMessageBox.critical(self, "Error", 
                               f"Error deleting conditioning record: {str(e)}")
    
    def record_rotation(self):
        """Record a rotation maintenance for the selected equipment."""
        # Prevent multiple triggers by disabling button immediately
        self.record_rotation_button.setEnabled(False)
        
        # Get the currently selected conditioning ID from the last selection
        if not hasattr(self, '_current_conditioning_id') or not self._current_conditioning_id:
            QMessageBox.warning(self, "No Selection", "Please select a conditioning record first.")
            self.record_rotation_button.setEnabled(True)  # Re-enable on error
            return
        
        # Get conditioning ID
        conditioning_id = self.conditioning_table.item(selected_row, 0).data(Qt.UserRole)
        
        # Get conditioning details
        conditioning = models.TyreMaintenance.get_by_id(conditioning_id)
        if not conditioning:
            return
        
        # Get equipment details
        equipment = models.Equipment.get_by_id(conditioning.get('equipment_id') or conditioning.get('EquipmentID'))
        if not equipment:
            return
        
        # Confirm action
        confirm = QMessageBox.question(
            self, "Record Rotation", 
            f"Record a rotation for {equipment.get('make_and_type') or equipment.get('MakeAndType')}?",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if confirm != QMessageBox.Yes:
            return
        
        try:
            # Create maintenance record
            today = date.today().isoformat()
            
            # Create maintenance model
            maintenance = models.Maintenance(
                equipment_id=equipment.get('equipment_id') or equipment.get('EquipmentID'),
                maintenance_type="Tyre Rotation",
                done_date=today,
                due_date=None,  # No due date needed
                vintage_years=equipment.get('VintageYears') or equipment.get('vintage_years'),
                meterage_kms=equipment.get('MeterageKMs') or equipment.get('meterage_kms')
            )
            
            # Save to database
            maintenance.save()
            
            # Update conditioning record
            conditioning_model = models.TyreMaintenance(
                tyre_maintenance_id=conditioning_id,
                equipment_id=conditioning.get('equipment_id') or conditioning.get('EquipmentID'),
                tyre_rotation_kms=conditioning.get('TyreRotationKMs') or conditioning.get('tyre_rotation_kms'),
                tyre_condition_kms=conditioning.get('TyreConditionKMs') or conditioning.get('tyre_condition_kms'),
                tyre_condition_years=conditioning.get('TyreConditionYears') or conditioning.get('tyre_condition_years'),
                last_rotation_date=today  # Update last rotation date
            )
            
            # Save to database
            conditioning_model.save()
            
            # Reload data
            self.load_data()
            
            # Reselect the row
            for row in range(self.conditioning_table.rowCount()):
                item = self.conditioning_table.item(row, 0)
                if item and item.data(Qt.UserRole) == conditioning_id:
                    self.conditioning_table.selectRow(row)
                    self.conditioning_selected(row)
                    break
            
            QMessageBox.information(self, "Success", 
                                  "Rotation recorded successfully.")
        except Exception as e:
            QMessageBox.critical(self, "Error", 
                               f"Error recording rotation: {str(e)}")
        
        # Re-enable button after operation complete
        self.record_rotation_button.setEnabled(True)
    
    def record_inspection(self):
        """Record an inspection maintenance for the selected equipment."""
        # Prevent multiple triggers by disabling button immediately
        self.record_inspection_button.setEnabled(False)
        
        # Get the currently selected conditioning ID from the last selection
        if not hasattr(self, '_current_conditioning_id') or not self._current_conditioning_id:
            QMessageBox.warning(self, "No Selection", "Please select a conditioning record first.")
            self.record_inspection_button.setEnabled(True)  # Re-enable on error
            return
        
        # Get conditioning ID
        conditioning_id = self.conditioning_table.item(selected_row, 0).data(Qt.UserRole)
        
        # Get conditioning details
        conditioning = models.TyreMaintenance.get_by_id(conditioning_id)
        if not conditioning:
            return
        
        # Get equipment details
        equipment = models.Equipment.get_by_id(conditioning.get('equipment_id') or conditioning.get('EquipmentID'))
        if not equipment:
            return
        
        # Confirm action
        confirm = QMessageBox.question(
            self, "Record Inspection", 
            f"Record an inspection for {equipment.get('make_and_type') or equipment.get('MakeAndType')}?",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if confirm != QMessageBox.Yes:
            return
        
        try:
            # Create maintenance record
            today = date.today().isoformat()
            
            # Create maintenance model
            maintenance = models.Maintenance(
                equipment_id=equipment.get('equipment_id') or equipment.get('EquipmentID'),
                maintenance_type="Tyre Inspection",
                done_date=today,
                due_date=None,  # No due date needed
                vintage_years=equipment.get('VintageYears') or equipment.get('vintage_years'),
                meterage_kms=equipment.get('MeterageKMs') or equipment.get('meterage_kms')
            )
            
            # Save to database
            maintenance.save()
            
            # Reload data
            self.load_data()
            
            # Reselect the row
            for row in range(self.conditioning_table.rowCount()):
                item = self.conditioning_table.item(row, 0)
                if item and item.data(Qt.UserRole) == conditioning_id:
                    self.conditioning_table.selectRow(row)
                    self.conditioning_selected(row)
                    break
            
            QMessageBox.information(self, "Success", 
                                  "Inspection recorded successfully.")
        except Exception as e:
            QMessageBox.critical(self, "Error", 
                               f"Error recording inspection: {str(e)}")
        
        # Re-enable button after operation complete
        self.record_inspection_button.setEnabled(True)
    
    def select_conditioning(self, conditioning_id):
        """Select conditioning with the given ID."""
        for row in range(self.conditioning_table.rowCount()):
            item = self.conditioning_table.item(row, 0)
            if item and item.data(Qt.UserRole) == conditioning_id:
                self.conditioning_table.selectRow(row)
                self.conditioning_selected(row)
                break
