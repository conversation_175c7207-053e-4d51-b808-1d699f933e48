"""Styles for the application UI."""

MAIN_STYLE = """
QMainWindow {
    background-color: #f5f5f5;
}

QTabWidget::pane {
    border: 1px solid #cccccc;
    background-color: #ffffff;
    border-radius: 5px;
}

QTabBar::tab {
    background-color: #e0e0e0;
    color: #333333;
    padding: 8px 16px;
    margin-right: 2px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    border: 1px solid #cccccc;
    border-bottom: none;
}

QTabBar::tab:selected {
    background-color: #ffffff;
    border-bottom-color: #ffffff;
}

QTabBar::tab:hover:!selected {
    background-color: #f0f0f0;
}

QTableWidget {
    border: 1px solid #cccccc;
    gridline-color: #f0f0f0;
    selection-background-color: #e7f5fe;
    selection-color: #000000;
}

QHeaderView::section {
    background-color: #f2f2f2;
    color: #333333;
    padding: 5px;
    border: 1px solid #cccccc;
    font-weight: bold;
}

QPushButton {
    background-color: #2196F3;
    color: white;
    border-radius: 4px;
    padding: 6px 12px;
    font-weight: bold;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #1976D2;
}

QPushButton:pressed {
    background-color: #0D47A1;
}

QPushButton:disabled {
    background-color: #BDBDBD;
    color: #757575;
}

QPushButton#deleteButton {
    background-color: #F44336;
}

QPushButton#deleteButton:hover {
    background-color: #D32F2F;
}

QPushButton#deleteButton:pressed {
    background-color: #B71C1C;
}

QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {
    border: 1px solid #BDBDBD;
    border-radius: 4px;
    padding: 5px;
    background-color: white;
}

QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {
    border: 1px solid #2196F3;
}

/* Fix for QComboBox dropdown hover issue */
QComboBox {
    selection-background-color: #e7f5fe;
    selection-color: #000000;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 15px;
    border-left-width: 1px;
    border-left-color: #BDBDBD;
    border-left-style: solid;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
    background-color: #f8f9fa;
}

QComboBox::down-arrow {
    image: none;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid #666666;
    width: 0px;
    height: 0px;
}

QComboBox QAbstractItemView {
    border: 1px solid #BDBDBD;
    background-color: white;
    selection-background-color: #e7f5fe;
    selection-color: #000000;
    outline: none;
}

QComboBox QAbstractItemView::item {
    min-height: 20px;
    padding: 4px 8px;
    background-color: white;
    color: #333333;
    border: none;
}

QComboBox QAbstractItemView::item:selected {
    background-color: #e7f5fe;
    color: #000000;
}

QComboBox QAbstractItemView::item:hover {
    background-color: #f0f8ff;
    color: #000000;
}

QComboBox QAbstractItemView::item:focus {
    background-color: #e7f5fe;
    color: #000000;
    outline: none;
}

QLabel {
    color: #333333;
}

QLabel#titleLabel {
    font-size: 18px;
    font-weight: bold;
    color: #333333;
}

QLabel#dashboardLabel {
    font-size: 14px;
    font-weight: bold;
    color: #333333;
}

QLabel#countLabel {
    font-size: 24px;
    font-weight: bold;
    color: #2196F3;
}

QLabel#alertLabel {
    font-weight: bold;
    color: #F44336;
}

QGroupBox {
    border: 1px solid #BDBDBD;
    border-radius: 5px;
    margin-top: 10px;
    font-weight: bold;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    padding: 0 5px;
    color: #333333;
}

QStatusBar {
    background-color: #f5f5f5;
    color: #333333;
}

QMenuBar {
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
}

QMenuBar::item {
    spacing: 5px;
    padding: 5px 10px;
    background: transparent;
}

QMenuBar::item:selected {
    background-color: #e0e0e0;
}

QMenu {
    background-color: #ffffff;
    border: 1px solid #cccccc;
}

QMenu::item {
    padding: 5px 20px 5px 20px;
}

QMenu::item:selected {
    background-color: #e7f5fe;
    color: #000000;
}

/* Dashboard widget styles */
QFrame#dashboardTile {
    border: 1px solid #BDBDBD;
    border-radius: 5px;
    background-color: white;
}

QFrame#alertTile {
    border: 1px solid #FFCDD2;
    border-radius: 5px;
    background-color: #FFEBEE;
}
"""

# Status-specific styles
STATUS_STYLES = {
    "normal": "color: #4CAF50;",
    "warning": "color: #FFC107; font-weight: bold;",
    "critical": "color: #F44336; font-weight: bold;",
    "info": "color: #2196F3;",
    "unknown": "color: #9E9E9E;"
}

# Chart colors
CHART_COLORS = [
    "#2196F3",  # Blue
    "#4CAF50",  # Green
    "#FFC107",  # Amber
    "#F44336",  # Red
    "#9C27B0",  # Purple
    "#FF9800",  # Orange
    "#795548",  # Brown
    "#607D8B",  # Blue Grey
    "#00BCD4",  # Cyan
    "#009688"   # Teal
]
