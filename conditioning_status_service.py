#!/usr/bin/env python3
"""
Centralized Conditioning Status Service

This service consolidates all conditioning status calculation logic that was
previously duplicated across UI components (conditioning_widget.py and 
dashboard_widget.py). It provides consistent status calculations for:
- Tyre rotation status
- Tyre condition status  
- Battery replacement status
"""

import logging
from datetime import datetime, date, timedelta
from typing import Optional, Dict, Any, Tuple

from conditioning_status_enums import (
    ConditioningStatus, TyreRotationStatus, TyreConditionStatus, BatteryStatus,
    ConditioningThresholds, StatusColors
)

logger = logging.getLogger(__name__)


class ConditioningStatusService:
    """Centralized service for all conditioning status calculations."""
    
    @staticmethod
    def calculate_tyre_rotation_status(
        current_meterage: float,
        last_rotation_date: Optional[str] = None,
        rotation_interval_kms: float = 0,
        equipment_meterage_at_last_rotation: Optional[float] = None
    ) -> TyreRotationStatus:
        """
        Calculate tyre rotation status based on meterage and rotation interval.
        
        Args:
            current_meterage: Current equipment meterage in KMs
            last_rotation_date: Date of last rotation (optional)
            rotation_interval_kms: KMs between rotations
            equipment_meterage_at_last_rotation: Meterage when last rotation was done
            
        Returns:
            TyreRotationStatus enum value
        """
        try:
            if rotation_interval_kms <= 0:
                return TyreRotationStatus.UNKNOWN
            
            # Calculate KMs since last rotation
            if equipment_meterage_at_last_rotation is not None:
                kms_since_rotation = current_meterage - equipment_meterage_at_last_rotation
            else:
                # Fallback: calculate based on the last rotation milestone
                # Find the last rotation milestone (multiple of interval)
                last_rotation_milestone = (current_meterage // rotation_interval_kms) * rotation_interval_kms
                kms_since_rotation = current_meterage - last_rotation_milestone

                # Special case: if exactly at a rotation milestone, consider it due for rotation
                if kms_since_rotation == 0 and current_meterage > 0:
                    kms_since_rotation = rotation_interval_kms

            # Calculate percentage of interval completed
            percentage_complete = kms_since_rotation / rotation_interval_kms
            
            # Apply thresholds
            if percentage_complete >= ConditioningThresholds.TYRE_ROTATION_OVERDUE_THRESHOLD:
                return TyreRotationStatus.OVERDUE
            elif percentage_complete >= ConditioningThresholds.TYRE_ROTATION_CRITICAL_THRESHOLD:
                return TyreRotationStatus.DUE_FOR_ROTATION
            elif percentage_complete >= ConditioningThresholds.TYRE_ROTATION_WARNING_THRESHOLD:
                return TyreRotationStatus.ROTATION_SOON
            else:
                return TyreRotationStatus.NORMAL
                
        except Exception as e:
            logger.error(f"Error calculating tyre rotation status: {e}")
            return TyreRotationStatus.UNKNOWN
    
    @staticmethod
    def calculate_tyre_condition_status(
        current_meterage: float,
        vintage_years: float,
        condition_kms: float = 0,
        condition_years: float = 0
    ) -> TyreConditionStatus:
        """
        Calculate tyre condition status based on meterage and age criteria.
        
        Args:
            current_meterage: Current equipment meterage in KMs
            vintage_years: Equipment age in years
            condition_kms: KMs threshold for condition check
            condition_years: Years threshold for condition check
            
        Returns:
            TyreConditionStatus enum value
        """
        try:
            # Check both KM and year criteria
            km_status = TyreConditionStatus.NORMAL
            year_status = TyreConditionStatus.NORMAL
            
            # Calculate KM-based status
            if condition_kms > 0:
                km_percentage = current_meterage / condition_kms
                
                if km_percentage >= ConditioningThresholds.TYRE_CONDITION_OVERDUE_THRESHOLD:
                    km_status = TyreConditionStatus.OVERDUE
                elif km_percentage >= ConditioningThresholds.TYRE_CONDITION_CRITICAL_THRESHOLD:
                    km_status = TyreConditionStatus.DUE_FOR_INSPECTION
                elif km_percentage >= ConditioningThresholds.TYRE_CONDITION_WARNING_THRESHOLD:
                    km_status = TyreConditionStatus.INSPECTION_SOON
            
            # Calculate year-based status
            if condition_years > 0:
                year_percentage = vintage_years / condition_years
                
                if year_percentage >= ConditioningThresholds.TYRE_CONDITION_OVERDUE_THRESHOLD:
                    year_status = TyreConditionStatus.OVERDUE
                elif year_percentage >= ConditioningThresholds.TYRE_CONDITION_CRITICAL_THRESHOLD:
                    year_status = TyreConditionStatus.DUE_FOR_INSPECTION
                elif year_percentage >= ConditioningThresholds.TYRE_CONDITION_WARNING_THRESHOLD:
                    year_status = TyreConditionStatus.INSPECTION_SOON
            
            # Return the most urgent status (earlier condition takes precedence)
            status_priority = {
                TyreConditionStatus.OVERDUE: 4,
                TyreConditionStatus.DUE_FOR_INSPECTION: 3,
                TyreConditionStatus.INSPECTION_SOON: 2,
                TyreConditionStatus.NORMAL: 1,
                TyreConditionStatus.UNKNOWN: 0
            }
            
            if condition_kms <= 0 and condition_years <= 0:
                return TyreConditionStatus.UNKNOWN
            
            # Return the status with higher priority
            if status_priority[km_status] >= status_priority[year_status]:
                return km_status
            else:
                return year_status
                
        except Exception as e:
            logger.error(f"Error calculating tyre condition status: {e}")
            return TyreConditionStatus.UNKNOWN
    
    @staticmethod
    def calculate_battery_status(
        done_date: Optional[str] = None,
        custom_life_months: Optional[int] = None
    ) -> BatteryStatus:
        """
        Calculate battery replacement status based on installation date and life expectancy.
        
        Args:
            done_date: Date when battery was installed/changed
            custom_life_months: Custom battery life in months (defaults to 24)
            
        Returns:
            BatteryStatus enum value
        """
        try:
            if not done_date:
                return BatteryStatus.UNKNOWN
            
            # Parse done date
            if isinstance(done_date, str):
                done_dt = datetime.strptime(done_date, '%Y-%m-%d')
            else:
                done_dt = done_date
            
            # Get battery life (default to 24 months)
            life_months = custom_life_months or ConditioningThresholds.DEFAULT_BATTERY_LIFE_MONTHS
            
            # Calculate due date
            due_dt = done_dt + timedelta(days=life_months * 30.44)  # Average month length
            
            # Calculate days until due
            today = datetime.now()
            days_until_due = (due_dt - today).days
            
            # Apply thresholds
            if days_until_due <= ConditioningThresholds.BATTERY_OVERDUE_DAYS:  # <= 0 (overdue)
                return BatteryStatus.OVERDUE
            elif days_until_due <= ConditioningThresholds.BATTERY_CRITICAL_DAYS:  # <= 7 days
                return BatteryStatus.DUE_FOR_REPLACEMENT
            elif days_until_due <= ConditioningThresholds.BATTERY_WARNING_DAYS:  # <= 30 days
                return BatteryStatus.REPLACEMENT_SOON
            else:
                return BatteryStatus.NORMAL
                
        except Exception as e:
            logger.error(f"Error calculating battery status: {e}")
            return BatteryStatus.UNKNOWN
    
    @staticmethod
    def get_kms_since_last_rotation(
        current_meterage: float,
        last_rotation_date: Optional[str] = None,
        equipment_meterage_at_last_rotation: Optional[float] = None,
        rotation_interval_kms: float = 0
    ) -> float:
        """
        Calculate kilometers since last rotation.
        
        Args:
            current_meterage: Current equipment meterage
            last_rotation_date: Date of last rotation
            equipment_meterage_at_last_rotation: Meterage at last rotation
            rotation_interval_kms: Rotation interval in KMs
            
        Returns:
            Kilometers since last rotation
        """
        try:
            if equipment_meterage_at_last_rotation is not None:
                return max(0, current_meterage - equipment_meterage_at_last_rotation)
            elif rotation_interval_kms > 0:
                # Estimate based on current meterage and interval
                return current_meterage % rotation_interval_kms
            else:
                return 0
        except Exception as e:
            logger.error(f"Error calculating KMs since last rotation: {e}")
            return 0
    
    @staticmethod
    def get_dashboard_alert_status(
        current_meterage: float,
        rotation_interval_kms: float,
        use_dashboard_thresholds: bool = True
    ) -> Tuple[bool, str]:
        """
        Calculate status for dashboard alerts with dashboard-specific thresholds.
        
        Args:
            current_meterage: Current equipment meterage
            rotation_interval_kms: Rotation interval in KMs
            use_dashboard_thresholds: Whether to use dashboard-specific thresholds
            
        Returns:
            Tuple of (should_alert, alert_level)
        """
        try:
            if rotation_interval_kms <= 0:
                return False, "normal"

            # Calculate KMs since last rotation
            # Find the last rotation milestone (multiple of interval)
            last_rotation_milestone = (current_meterage // rotation_interval_kms) * rotation_interval_kms
            kms_since_rotation = current_meterage - last_rotation_milestone

            # Special case: if exactly at a rotation milestone, consider it due for rotation
            if kms_since_rotation == 0 and current_meterage > 0:
                kms_since_rotation = rotation_interval_kms

            # Calculate percentage of interval completed
            percentage_complete = kms_since_rotation / rotation_interval_kms

            # Check if overdue (100% or more)
            if percentage_complete >= 1.0:
                return True, "critical"

            # Use dashboard-specific thresholds if requested
            if use_dashboard_thresholds:
                if percentage_complete >= ConditioningThresholds.DASHBOARD_ALERT_WARNING_THRESHOLD:
                    return True, "warning"

            return False, "normal"
            
        except Exception as e:
            logger.error(f"Error calculating dashboard alert status: {e}")
            return False, "unknown"
    
    @staticmethod
    def get_status_color(status) -> str:
        """Get color for any conditioning status."""
        return StatusColors.get_status_color(status)
    
    @staticmethod
    def get_equipment_conditioning_summary(equipment_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get comprehensive conditioning status summary for equipment.
        
        Args:
            equipment_data: Dictionary containing equipment and conditioning data
            
        Returns:
            Dictionary with all conditioning status information
        """
        try:
            current_meterage = float(equipment_data.get('meterage_kms', 0))
            vintage_years = float(equipment_data.get('vintage_years', 0))
            
            # Tyre rotation data
            rotation_kms = float(equipment_data.get('tyre_rotation_kms', 0))
            last_rotation_date = equipment_data.get('last_rotation_date')
            
            # Tyre condition data
            condition_kms = float(equipment_data.get('tyre_condition_kms', 0))
            condition_years = float(equipment_data.get('tyre_condition_years', 0))
            
            # Battery data
            battery_done_date = equipment_data.get('battery_done_date')
            battery_life_months = equipment_data.get('custom_life_months')
            
            # Calculate all statuses
            rotation_status = ConditioningStatusService.calculate_tyre_rotation_status(
                current_meterage, last_rotation_date, rotation_kms
            )
            
            condition_status = ConditioningStatusService.calculate_tyre_condition_status(
                current_meterage, vintage_years, condition_kms, condition_years
            )
            
            battery_status = ConditioningStatusService.calculate_battery_status(
                battery_done_date, battery_life_months
            )
            
            kms_since_rotation = ConditioningStatusService.get_kms_since_last_rotation(
                current_meterage, last_rotation_date, None, rotation_kms
            )
            
            return {
                'rotation_status': rotation_status,
                'condition_status': condition_status,
                'battery_status': battery_status,
                'kms_since_rotation': kms_since_rotation,
                'rotation_status_color': StatusColors.get_status_color(rotation_status),
                'condition_status_color': StatusColors.get_status_color(condition_status),
                'battery_status_color': StatusColors.get_status_color(battery_status)
            }
            
        except Exception as e:
            logger.error(f"Error getting equipment conditioning summary: {e}")
            return {
                'rotation_status': TyreRotationStatus.UNKNOWN,
                'condition_status': TyreConditionStatus.UNKNOWN,
                'battery_status': BatteryStatus.UNKNOWN,
                'kms_since_rotation': 0,
                'rotation_status_color': StatusColors.UNKNOWN_COLOR,
                'condition_status_color': StatusColors.UNKNOWN_COLOR,
                'battery_status_color': StatusColors.UNKNOWN_COLOR
            }


# Convenience instance for easy importing
conditioning_status_service = ConditioningStatusService()
