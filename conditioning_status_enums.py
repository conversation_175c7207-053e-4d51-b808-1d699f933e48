#!/usr/bin/env python3
"""
Conditioning Status Enums and Configuration

This module provides standardized status enums and centralized configuration
for conditioning status calculations, replacing the inconsistent values
scattered across UI components.
"""

from enum import Enum
from datetime import timedelta


class ConditioningStatus(Enum):
    """Standardized conditioning status values."""
    NORMAL = "normal"
    WARNING = "warning"
    CRITICAL = "critical"
    OVERDUE = "overdue"
    UNKNOWN = "unknown"
    
    def __str__(self):
        return self.value


class TyreRotationStatus(Enum):
    """Specific tyre rotation status values with display text."""
    NORMAL = ("normal", "Normal")
    ROTATION_SOON = ("warning", "Rotation Soon")
    DUE_FOR_ROTATION = ("critical", "Due for Rotation")
    OVERDUE = ("overdue", "Rotation Overdue")
    UNKNOWN = ("unknown", "Unknown")
    
    def __init__(self, status_level, display_text):
        self.status_level = status_level
        self.display_text = display_text
    
    def __str__(self):
        return self.display_text


class TyreConditionStatus(Enum):
    """Specific tyre condition status values with display text."""
    NORMAL = ("normal", "Normal")
    INSPECTION_SOON = ("warning", "Inspection Soon")
    DUE_FOR_INSPECTION = ("critical", "Due for Inspection")
    OVERDUE = ("overdue", "Inspection Overdue")
    UNKNOWN = ("unknown", "Unknown")
    
    def __init__(self, status_level, display_text):
        self.status_level = status_level
        self.display_text = display_text
    
    def __str__(self):
        return self.display_text


class BatteryStatus(Enum):
    """Specific battery status values with display text."""
    NORMAL = ("normal", "Normal")
    REPLACEMENT_SOON = ("warning", "Replacement Soon")
    DUE_FOR_REPLACEMENT = ("critical", "Due for Replacement")
    OVERDUE = ("overdue", "Replacement Overdue")
    UNKNOWN = ("unknown", "Unknown")
    
    def __init__(self, status_level, display_text):
        self.status_level = status_level
        self.display_text = display_text
    
    def __str__(self):
        return self.display_text


class ConditioningThresholds:
    """Centralized configuration for conditioning status thresholds."""
    
    # Tyre Rotation Thresholds (percentage of rotation interval)
    TYRE_ROTATION_WARNING_THRESHOLD = 0.80  # 80% - standardized from 80% vs 90% inconsistency
    TYRE_ROTATION_CRITICAL_THRESHOLD = 1.00  # 100%
    TYRE_ROTATION_OVERDUE_THRESHOLD = 1.20   # 120% - significantly overdue
    
    # Tyre Condition Thresholds (percentage of condition interval)
    TYRE_CONDITION_WARNING_THRESHOLD = 0.80  # 80%
    TYRE_CONDITION_CRITICAL_THRESHOLD = 1.00  # 100%
    TYRE_CONDITION_OVERDUE_THRESHOLD = 1.20   # 120%
    
    # Battery Replacement Thresholds (days before/after due date)
    BATTERY_WARNING_DAYS = 30    # Warning 30 days before due
    BATTERY_CRITICAL_DAYS = 7    # Critical 7 days before due
    BATTERY_OVERDUE_DAYS = 0     # Overdue after due date
    
    # Default Battery Life (months)
    DEFAULT_BATTERY_LIFE_MONTHS = 24  # 2 years
    
    # Alert Dashboard Thresholds (for dashboard alerts)
    DASHBOARD_ALERT_WARNING_THRESHOLD = 0.90  # 90% for dashboard warnings
    DASHBOARD_ALERT_CRITICAL_THRESHOLD = 1.00  # 100% for dashboard critical
    
    @classmethod
    def get_tyre_rotation_thresholds(cls):
        """Get tyre rotation thresholds as a dictionary."""
        return {
            'warning': cls.TYRE_ROTATION_WARNING_THRESHOLD,
            'critical': cls.TYRE_ROTATION_CRITICAL_THRESHOLD,
            'overdue': cls.TYRE_ROTATION_OVERDUE_THRESHOLD
        }
    
    @classmethod
    def get_tyre_condition_thresholds(cls):
        """Get tyre condition thresholds as a dictionary."""
        return {
            'warning': cls.TYRE_CONDITION_WARNING_THRESHOLD,
            'critical': cls.TYRE_CONDITION_CRITICAL_THRESHOLD,
            'overdue': cls.TYRE_CONDITION_OVERDUE_THRESHOLD
        }
    
    @classmethod
    def get_battery_thresholds(cls):
        """Get battery thresholds as a dictionary."""
        return {
            'warning_days': cls.BATTERY_WARNING_DAYS,
            'critical_days': cls.BATTERY_CRITICAL_DAYS,
            'overdue_days': cls.BATTERY_OVERDUE_DAYS,
            'default_life_months': cls.DEFAULT_BATTERY_LIFE_MONTHS
        }
    
    @classmethod
    def get_dashboard_thresholds(cls):
        """Get dashboard-specific thresholds."""
        return {
            'warning': cls.DASHBOARD_ALERT_WARNING_THRESHOLD,
            'critical': cls.DASHBOARD_ALERT_CRITICAL_THRESHOLD
        }


class StatusColors:
    """Centralized color configuration for status display."""
    
    # Status Colors (hex values) - Updated to match main status color scheme
    NORMAL_COLOR = "#33cc33"      # Bright Green (matches completed)
    WARNING_COLOR = "#669900"     # Olive Green (matches upcoming - for warning level)
    CRITICAL_COLOR = "#ff6600"    # Orange-Red (matches critical)
    OVERDUE_COLOR = "#cc0000"     # Red (matches overdue)
    UNKNOWN_COLOR = "#cc33ff"     # Purple/Magenta (matches unknown)
    
    @classmethod
    def get_status_color(cls, status):
        """Get color for a given status."""
        if isinstance(status, (TyreRotationStatus, TyreConditionStatus, BatteryStatus)):
            status = status.status_level
        elif isinstance(status, ConditioningStatus):
            status = status.value
        
        color_map = {
            'normal': cls.NORMAL_COLOR,
            'warning': cls.WARNING_COLOR,
            'critical': cls.CRITICAL_COLOR,
            'overdue': cls.OVERDUE_COLOR,
            'unknown': cls.UNKNOWN_COLOR
        }
        
        return color_map.get(status, cls.UNKNOWN_COLOR)
    
    @classmethod
    def get_all_colors(cls):
        """Get all status colors as a dictionary."""
        return {
            'normal': cls.NORMAL_COLOR,
            'warning': cls.WARNING_COLOR,
            'critical': cls.CRITICAL_COLOR,
            'overdue': cls.OVERDUE_COLOR,
            'unknown': cls.UNKNOWN_COLOR
        }


# Convenience functions for backward compatibility
def get_status_color(status):
    """Convenience function to get status color."""
    return StatusColors.get_status_color(status)


def get_conditioning_thresholds():
    """Get all conditioning thresholds for backward compatibility."""
    return {
        'tyre_rotation': ConditioningThresholds.get_tyre_rotation_thresholds(),
        'tyre_condition': ConditioningThresholds.get_tyre_condition_thresholds(),
        'battery': ConditioningThresholds.get_battery_thresholds(),
        'dashboard': ConditioningThresholds.get_dashboard_thresholds()
    }
