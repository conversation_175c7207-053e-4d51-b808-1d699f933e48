"""
Excel import utility for InventoryTracker application.
UPDATED: Now uses the new robust staging database approach for improved data validation and processing.
"""
import os
import pandas as pd
import logging

# List of all standardized column names used for parsing Excel data
PARSING_COLUMN_NAMES = [
    'serial_number',
    'make_and_type',
    'ba_number',
    'units_held',
    'vintage_years',
    'date_of_commission',
    'date_of_induction',
    'equipment_status',
    'remarks',
    'meterage_kms',
    'meterage_description',
    'km_hrs_run_previous_month',
    'km_hrs_run_current_month',
    'hours_run_total',
    'hours_run_previous_month',
    'hours_run_current_month',
    'oh1_done_date',
    'oh1_due_date',
    'oh2_done_date',
    'oh2_due_date',
    'fluid_type',
    'capacity_ltrs_kg',
    'addl_10_percent_top_up',
    'top_up_percent',
    'grade',
    'periodicity_km',
    'periodicity_hrs',
    'periodicity_months',
    'last_serviced_date',
    'last_serviced_meterage',
    'date_of_change',
    'maintenance_type',
    'done_date',
    'due_date',
    'completion_notes',
    'status',
    'completed_by',
    'actual_completion_date',
    'completion_meterage',
    'maintenance_category',
    'repair_type',
    'repair_date',
    'criteria_years',
    'criteria_kms',
    'criteria_hours',
    'discard_reason',
    'discard_date',
    'tyre_rotation_kms',
    'tyre_condition_kms',
    'tyre_condition_years',
    'last_rotation_date',
    'quantity',
    'battery_type',
    'battery_capacity',
    'battery_voltage',
    'battery_date_of_change',
    'overhaul_type',
    'overhaul_due_date',
    'overhaul_done_date',
    'medium_reset_id',
    'reset_type',
    'mr1_due_date',
    'mr2_due_date',
    'discard_due_date',
    'conditioning_type',
    'conditioning_kms',
    'conditioning_years',
    'fiscal_year',
    'total_requirement',
    'replacement_reason',
    'remarks',
    'is_active',
]

"""
PARSING_COLUMN_NAMES contains all standardized column names expected from Excel import. Each name maps to a field in one or more of the following models:
- Equipment
- Fluid
- Maintenance
- Repair
- DiscardCriteria
- TyreMaintenance
- Battery
- Overhaul
- MediumReset
- Forecast models (DemandForecast, TyreForecast, BatteryForecast, EquipmentForecast, OverhaulForecast, ConditioningForecast)
"""

import re
from datetime import date
from dateutil import parser as date_parser
# Only import robust importer
from robust_excel_importer_working import RobustExcelImporter

logger = logging.getLogger('excel_importer')

def parse_excel_date(val):
    if pd.isna(val) or str(val).strip() in ('', '#######', '-', 'NA', 'N/A', 'nan', 'NaN'):
        return None
    
    try:
        # Handle pandas Timestamp and Python date objects
        if isinstance(val, (pd.Timestamp, date)):
            return val.strftime('%Y-%m-%d')
        
        # Handle numeric values (Excel serial numbers)
        if isinstance(val, (int, float)):
            # Excel dates are stored as days since 1900-01-01 (with 1900-01-01 = 1)
            # But Excel incorrectly treats 1900 as a leap year, so we need to adjust
            if 1 <= val <= 2958465:  # Valid Excel date range (1900-01-01 to 9999-12-31)
                try:
                    # Convert Excel serial number to date
                    excel_epoch = pd.Timestamp('1899-12-30')  # Excel's epoch (accounting for the leap year bug)
                    parsed_date = excel_epoch + pd.Timedelta(days=val)
                    return parsed_date.strftime('%Y-%m-%d')
                except:
                    pass  # Fall through to string parsing
        
        # Handle string values
        s = str(val).replace('\\', '/').replace('.', '/').replace('-', '/').strip().upper()
        
        # Skip obvious non-date strings
        if any(keyword in s for keyword in ['UNDER', 'MLOH', 'YRS', 'YEAR', 'MONTH', 'NA', 'NULL']):
            return None
        
        # Handle multiple dates in one cell (take the first one)
        if len(s.split()) > 1 and any(char in s for char in ['/', '-']):
            # Split by spaces and take the first date-like part
            parts = s.split()
            for part in parts:
                if any(char in part for char in ['/', '-']) and len(part) >= 6:
                    s = part
                    break
        
        # Handle DD/MM/YYYY format (common in your data like 23/07/2000)
        if '/' in s and len(s.split('/')) == 3:
            parts = s.split('/')
            try:
                if len(parts[0]) <= 2 and len(parts[1]) <= 2 and len(parts[2]) >= 2:
                    # Fix common typos like '23/03/20211' -> '23/03/2021'
                    if len(parts[2]) > 4:
                        parts[2] = parts[2][:4]
                    
                    # Validate day and month ranges
                    day, month, year = int(parts[0]), int(parts[1]), int(parts[2])
                    
                    # Convert 2-digit years to 4-digit (assumes 20xx for 00-30, 19xx for 31-99)
                    if year < 100:
                        year = 2000 + year if year <= 30 else 1900 + year
                    
                    # Validate date components
                    if 1 <= day <= 31 and 1 <= month <= 12 and 1900 <= year <= 2100:
                        # Check for invalid dates like 31/06 (June doesn't have 31 days)
                        try:
                            test_date = date(year, month, day)
                            return test_date.strftime('%Y-%m-%d')
                        except ValueError:
                            # Invalid date (e.g., 31/06/25)
                            return None
            except (ValueError, IndexError):
                pass
        
        # Try to parse with dateutil as fallback
        try:
            parsed_date = date_parser.parse(s, dayfirst=True)
            # Validate the parsed year is reasonable
            if 1900 <= parsed_date.year <= 2100:
                return parsed_date.strftime('%Y-%m-%d')
        except:
            pass
            
        return None
        
    except Exception as e:
        logger.debug(f"Could not parse date '{val}': {e}")
        return None

def parse_int(val):
    if pd.isna(val) or str(val).strip() in ('', '#######', '-', 'NA', 'N/A'):
        return 0
    s = str(val).replace(',', '').upper()
    
    # Handle range formats like "8 to 10 Year" - take the average
    if ' TO ' in s:
        try:
            parts = s.replace('YEAR', '').replace('YRS', '').replace('YR', '').split(' TO ')
            if len(parts) == 2:
                start = float(re.sub(r'[^0-9\.-]', '', parts[0].strip()))
                end = float(re.sub(r'[^0-9\.-]', '', parts[1].strip()))
                return int((start + end) / 2)  # Return average
        except Exception:
            pass
    
    # Handle complex formats like "5000 Hrs or 105000" - extract the larger number (likely KM)
    if ' OR ' in s:
        try:
            parts = s.split(' OR ')
            numbers = []
            for part in parts:
                # Extract numbers from each part
                num_str = re.sub(r'[^0-9\.-]', '', part.strip())
                if num_str:
                    numbers.append(float(num_str))
            if numbers:
                return int(max(numbers))  # Return the larger number
        except Exception:
            pass
    
    if 'YR' in s:
        s = s.split('YR')[0]
    if 'KM' in s:
        s = s.split('KM')[0]
    try:
        return int(float(s))
    except Exception:
        return 0

def parse_km_hrs(val):
    """Parse combined KM/HRS values like '625/267' or '1785/1756.2' into separate KM and HRS values."""
    if pd.isna(val) or str(val).strip() in ('', '#######', '-', 'NA', 'N/A'):
        return 0.0, 0.0
    
    s = str(val).replace(',', '').strip()
    
    # Handle slash-separated values (KM/HRS format)
    if '/' in s:
        try:
            parts = s.split('/')
            if len(parts) >= 2:
                km_part = parts[0].strip()
                hrs_part = parts[1].strip()
                
                # Extract numeric values
                km = float(re.sub(r'[^0-9\.-]', '', km_part)) if km_part else 0.0
                hrs = float(re.sub(r'[^0-9\.-]', '', hrs_part)) if hrs_part else 0.0
                
                return km, hrs
        except Exception:
            pass
    
    # Fallback: treat as single numeric value (assume it's KM)
    try:
        single_val = float(re.sub(r'[^0-9\.-]', '', s))
        return single_val, 0.0
    except Exception:
        return 0.0, 0.0

def parse_km_yrs(val):
    # Handles '10000 KM/10 YRS' or '10000 KM/10 YRS'
    if pd.isna(val) or str(val).strip() in ('', '#######', '-', 'NA', 'N/A'):
        return 0, 0
    s = str(val).upper().replace(' ', '')
    km, yrs = 0, 0
    if '/' in s:
        parts = s.split('/')
        for p in parts:
            if 'KM' in p:
                km = parse_int(p)
            elif 'YR' in p:
                yrs = parse_int(p)
    else:
        if 'KM' in s:
            km = parse_int(s)
        if 'YR' in s:
            yrs = parse_int(s)
    return km, yrs

def import_from_excel(file_path):
    """
    Import data from an Excel file into the database using enhanced processing.

    This function now uses the EnhancedExcelImporter which:
    - Automatically detects system capabilities and chooses optimal processing strategy
    - Ensures complete workbook processing across all deployment systems
    - Provides comprehensive error handling and recovery mechanisms
    - Supports all entity types with robust data type detection
    - Addresses deployment issues with partial data imports

    Args:
        file_path (str): Path to the Excel file to import

    Returns:
        dict: Import statistics with counts of imported records by type
    """
    logger.info(f"Starting enhanced Excel import from {file_path}")

    try:
        # Check file size and system constraints
        file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
        logger.info(f"Excel file size: {file_size_mb:.1f}MB")

        if file_size_mb > 500:  # Hard limit for military systems
            raise ValueError(f"Excel file too large ({file_size_mb:.1f}MB). Maximum supported size is 500MB.")

        # Use enhanced importer that automatically chooses the best strategy
        logger.info("Using enhanced Excel importer with automatic strategy selection")
        from enhanced_excel_importer import import_excel_enhanced

        result = import_excel_enhanced(file_path)

        # Convert enhanced importer stats to legacy format for compatibility
        legacy_stats = {
            'equipment': result.get('equipment', 0),
            'fluids': result.get('fluids', 0),
            'tyres': result.get('tyre_maintenance', 0) + result.get('conditioning', 0),  # Combine tyres and conditioning
            'batteries': result.get('batteries', 0),
            'repairs': result.get('repairs', 0),
            'medium_resets': result.get('medium_resets', 0),
            'overhauls': result.get('overhauls', 0),
            'maintenance': result.get('maintenance', 0),
            'discard_criteria': result.get('discard_criteria', 0),
            'skipped': 0,
            'errors': result.get('errors', [])
        }

        # If enhanced importer failed, fall back to robust importer
        total_imported = sum(legacy_stats.get(key, 0) for key in [
            'equipment', 'fluids', 'tyres', 'batteries', 'repairs', 'overhauls', 'maintenance', 'discard_criteria'
        ])

        # Check if critical data types are missing (enhanced fallback logic)
        equipment_count = legacy_stats.get('equipment', 0)
        critical_data_types = ['fluids', 'maintenance', 'overhauls']
        missing_critical_data = all(legacy_stats.get(key, 0) == 0 for key in critical_data_types)
        
        # Fallback if: 1) No data at all, OR 2) Equipment imported but NO critical data types
        should_fallback = (
            (total_imported == 0 and not legacy_stats.get('errors')) or
            (equipment_count > 0 and missing_critical_data and not legacy_stats.get('errors'))
        )
        
        if should_fallback:
            fallback_reason = "no data" if total_imported == 0 else f"equipment imported ({equipment_count}) but no critical data (fluids/maintenance/overhauls)"
            logger.warning(f"Enhanced importer issue detected: {fallback_reason}, falling back to robust importer")
            return _fallback_to_robust_importer(file_path)

        logger.info(f"Enhanced Excel import completed with {total_imported} total records")
        return legacy_stats
        
    except Exception as e:
        logger.error(f"Error in robust Excel import: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {
            'equipment': 0,
            'fluids': 0,
            'tyres': 0,
            'batteries': 0,
            'repairs': 0,
            'medium_resets': 0,
            'overhauls': 0,
            'maintenance': 0,
            'discard_criteria': 0,
            'skipped': 0,
            'errors': [f"Import error: {str(e)}"]
        }


def _fallback_to_robust_importer(file_path):
    """Fallback to robust importer when enhanced importer fails."""
    logger.info("Using fallback robust importer")

    try:
        importer = RobustExcelImporter()

        # Initialize staging database
        if not importer.initialize_staging():
            logger.error("Failed to initialize staging database")
            return {
                'equipment': 0,
                'fluids': 0,
                'tyres': 0,
                'batteries': 0,
                'repairs': 0,
                'medium_resets': 0,
                'overhauls': 0,
                'maintenance': 0,
                'discard_criteria': 0,
                'skipped': 0,
                'errors': ['Failed to initialize staging database']
            }

        # Process the Excel file
        success, stats = importer.process_excel_file(file_path)

        if not success:
            logger.error(f"Fallback Excel import failed: {stats}")
            return {
                'equipment': 0,
                'fluids': 0,
                'tyres': 0,
                'batteries': 0,
                'repairs': 0,
                'medium_resets': 0,
                'overhauls': 0,
                'maintenance': 0,
                'discard_criteria': 0,
                'skipped': 0,
                'errors': [f"Import failed: {stats}"]
            }

        # Convert robust importer stats to legacy format for compatibility
        legacy_stats = {
            'equipment': stats.get('total_equipment', 0),
            'fluids': stats.get('total_fluids', 0),
            'tyres': stats.get('total_tyres', 0) + stats.get('total_conditioning', 0),  # Combine tyres and conditioning
            'batteries': stats.get('total_batteries', 0),
            'repairs': stats.get('total_repairs', 0),
            'overhauls': stats.get('total_overhauls', 0),
            'maintenance': stats.get('total_maintenance', 0),
            'discard_criteria': stats.get('total_discard_criteria', 0),
            'skipped': stats.get('total_skipped', 0)
        }

        logger.info(f"Fallback Excel import completed with stats: {legacy_stats}")
        return legacy_stats

    except Exception as e:
        logger.error(f"Error in fallback Excel import: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {
            'equipment': 0,
            'fluids': 0,
            'tyres': 0,
            'batteries': 0,
            'repairs': 0,
            'medium_resets': 0,
            'overhauls': 0,
            'maintenance': 0,
            'discard_criteria': 0,
            'skipped': 0,
            'errors': [f"Import error: {str(e)}"]
        }


def import_all_sheets_from_excel(file_path):
    """
    Import data from all sheets in an Excel workbook, using flexible mapping and robust error handling.
    Returns a summary of import results for each sheet.
    """
    import pandas as pd
    logger.info(f"Starting full-sheet Excel import from {file_path}")
    results = {}
    try:
        excel_file = pd.ExcelFile(file_path)
        for sheet_name in excel_file.sheet_names:
            try:
                logger.info(f"Processing sheet: {sheet_name}")
                df = None
                # Try to read with multiple header configs (like RobustExcelImporter)
                for header_config in [[0, 1, 2], [0, 1], 0]:
                    try:
                        df = pd.read_excel(excel_file, sheet_name=sheet_name, header=header_config)
                        # Flatten multi-level columns
                        if hasattr(df, 'columns') and isinstance(df.columns, pd.MultiIndex):
                            df.columns = [' '.join(str(col).strip() for col in cols if str(col) != 'nan') for cols in df.columns.values]
                        if len(df.columns) > 3 and len(df) > 0:
                            break
                    except Exception:
                        continue
                if df is None or len(df.columns) <= 3 or len(df) == 0:
                    logger.warning(f"Skipped sheet {sheet_name}: could not detect valid data table.")
                    results[sheet_name] = {'status': 'skipped', 'reason': 'Invalid or empty sheet'}
                    continue
                # Try to detect type by columns (simple heuristic: look for key fields)
                cols_lower = [str(c).lower() for c in df.columns]
                if any('make' in c and 'type' in c for c in cols_lower):
                    entity_type = 'equipment'
                elif any('fluid' in c for c in cols_lower):
                    entity_type = 'fluids'
                elif any('maint' in c for c in cols_lower):
                    entity_type = 'maintenance'
                elif any('tyre' in c for c in cols_lower):
                    entity_type = 'tyres'
                elif any('batter' in c for c in cols_lower):
                    entity_type = 'batteries'
                elif any('repair' in c for c in cols_lower):
                    entity_type = 'repairs'
                elif any('discard' in c for c in cols_lower):
                    entity_type = 'discard_criteria'
                else:
                    entity_type = 'unknown'
                # Attempt import using RobustExcelImporter if entity_type is recognized
                if entity_type != 'unknown':
                    try:
                        importer = RobustExcelImporter()
                        if not importer.initialize_staging():
                            results[sheet_name] = {'status': 'error', 'reason': 'Failed to initialize staging db'}
                            continue
                        # Use only the relevant extraction method
                        if entity_type == 'equipment':
                            count = importer._extract_and_save_equipment(df, sheet_name)
                        elif entity_type == 'fluids':
                            count = importer._extract_and_save_fluids(df, sheet_name)
                        elif entity_type == 'maintenance':
                            count = importer._extract_and_save_maintenance(df, sheet_name)
                        elif entity_type == 'tyres':
                            count = importer._extract_and_save_conditioning(df, sheet_name)
                        elif entity_type == 'batteries':
                            count = importer._extract_and_save_batteries(df, sheet_name)
                        elif entity_type == 'repairs':
                            count = importer._extract_and_save_repairs(df, sheet_name)
                        elif entity_type == 'discard_criteria':
                            count = importer._extract_and_save_discard_criteria(df, sheet_name)
                        else:
                            count = 0
                        results[sheet_name] = {'status': 'imported', 'entity_type': entity_type, 'rows_imported': count}
                    except Exception as e:
                        logger.error(f"Error importing {entity_type} from {sheet_name}: {e}")
                        results[sheet_name] = {'status': 'error', 'entity_type': entity_type, 'reason': str(e)}
                else:
                    logger.warning(f"Skipped sheet {sheet_name}: could not detect entity type.")
                    results[sheet_name] = {'status': 'skipped', 'reason': 'Unknown entity type'}
            except Exception as e:
                logger.error(f"Critical error processing sheet {sheet_name}: {e}")
                results[sheet_name] = {'status': 'error', 'reason': str(e)}
        logger.info(f"Full-sheet Excel import completed with results: {results}")
        return results
    except Exception as e:
        logger.error(f"Error in full-sheet Excel import: {e}")
        return {'error': str(e)}

def test_excel_column_detection(file_path):
    """Test function to analyze Excel file structure."""
    try:
        from enhanced_excel_importer import EnhancedExcelImporter

        importer = EnhancedExcelImporter(file_path)

        if not importer.validate_excel_file():
            print(f"Excel file validation failed: {file_path}")
            return

        sheet_names = importer.get_all_sheet_names()
        print(f"Found {len(sheet_names)} sheets: {sheet_names}")

        # Test import
        result = importer.import_all_data()
        print(f"Import result: {result}")

    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()


# Add this at the end of the file for debugging
if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        test_excel_column_detection(sys.argv[1])
    else:
        print("Usage: python excel_importer.py <excel_file_path>")